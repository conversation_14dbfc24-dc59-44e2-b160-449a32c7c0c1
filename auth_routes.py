import os
import traceback
from typing import Optional

import requests
from authlib.integrations.httpx_client import AsyncOAuth2Client
from authlib.integrations.starlette_client import OAuth
from authlib.jose import <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from dotenv import load_dotenv
from fastapi import APIRouter, HTTPException, Request
from fastapi.responses import RedirectResponse
from pydantic import BaseModel

# Load environment variables
load_dotenv()

router = APIRouter(prefix="/auth", tags=["Authentication"])


# --- Token request model ---
class TokenRequest(BaseModel):
    code: str
    redirect_uri: str
    state: Optional[str] = None


# --- OAuth2 Client Setup ---
oauth = OAuth()
oauth_config = {
    "name": "dex",
    "client_id": os.getenv("AUTH_CLIENT_ID"),
    "client_secret": os.getenv("AUTH_CLIENT_SECRET"),
    "access_token_url": os.getenv("AUTH_TOKEN_URL"),
    "authorize_url": os.getenv("AUTH_AUTHORIZE_URL"),
    "client_kwargs": {"scope": "openid email profile"},
}
oauth.register(**oauth_config)

DEX_ISSUER = os.getenv("AUTH_ISSUER", "")
JWKS_URL = os.getenv("AUTH_JWKS_URI", f"{DEX_ISSUER}/keys")


# --- JWT Parser ---
def parse_dex_id_token(id_token: str) -> dict:
    try:
        jwks = JsonWebKey.import_key_set(requests.get(JWKS_URL).json())
        claims = jwt.decode(id_token, key=jwks)
        claims.validate()  # Only checks exp, nbf, etc.

        # Manual issuer validation
        if claims.get("iss") != DEX_ISSUER:
            raise JoseError(f"Issuer mismatch: {claims.get('iss')} != {DEX_ISSUER}")

        return dict(claims)
    except JoseError as e:
        print(f"❌ Failed to validate token: {e}")
        raise HTTPException(status_code=401, detail="Invalid ID token")


# === OAuth Login Redirect ===
@router.get("/login")
async def login(request: Request):
    redirect_uri = os.getenv("AUTH_REDIRECT_URI")
    return await oauth.dex.authorize_redirect(request, redirect_uri)


# === Callback After Dex Login ===
@router.get("/callback")
async def callback(request: Request):
    try:
        token = await oauth.dex.authorize_access_token(request)
        user = await oauth.dex.parse_id_token(request, token)

        # Check if we have valid user information
        if not user:
            print("❌ No valid user information found")
            return RedirectResponse(
                url=f"{os.getenv('FRONTEND_URL')}/login?error=no_user_info"
            )

        request.session["user"] = dict(user)
        return RedirectResponse(url=f"{os.getenv('FRONTEND_URL')}/")
    except Exception as e:
        print(f"Error in callback: {str(e)}")
        return RedirectResponse(
            url=f"{os.getenv('FRONTEND_URL')}/login?error=authentication_failed"
        )


# === Check if User is Logged In ===
@router.get("/userinfo")
async def userinfo(request: Request):
    user = request.session.get("user")
    return {"authenticated": bool(user), "user": user}


# === Logout ===
@router.get("/logout")
async def logout(request: Request):
    request.session.pop("user", None)
    return RedirectResponse(url=os.getenv("AUTH_LOGOUT_URL"))


# === Code-to-Token Exchange (for frontend) ===
@router.post("/token")
async def exchange_token(request_data: TokenRequest, request: Request):
    try:
        print("🔁 Token exchange request received")

        client = AsyncOAuth2Client(
            client_id=os.getenv("AUTH_CLIENT_ID"),
            client_secret=os.getenv("AUTH_CLIENT_SECRET"),
            token_endpoint=os.getenv("AUTH_TOKEN_URL"),
        )

        token = await client.fetch_token(
            url=os.getenv("AUTH_TOKEN_URL"),
            code=request_data.code,
            redirect_uri=request_data.redirect_uri,
        )

        id_token = token.get("id_token")
        user_info = {}

        if id_token:
            try:
                user_info = parse_dex_id_token(id_token)
                print("✅ Parsed user info from ID token")
            except Exception as e:
                print(f"⚠️ Failed to parse ID token: {e}")

        # If we couldn't get user info from the ID token and there's no fallback info in the token
        if not user_info and not (
            token.get("sub") or token.get("email") or token.get("name")
        ):
            print("❌ No valid user information found in token")
            raise HTTPException(
                status_code=401,
                detail="Authentication failed: No valid user information found",
            )

        # Only use fallback if we have at least some basic info in the token
        if not user_info:
            user_info = {
                "sub": token.get("sub", "user"),
                "email": token.get("email", "<EMAIL>"),
                "name": token.get("name", "Authenticated User"),
            }
            print("⚠️ Using fallback user info from token")

        request.session["user"] = user_info

        return {
            "access_token": token.get("access_token"),
            "token_type": token.get("token_type", "bearer"),
            "expires_in": token.get("expires_in", 3600),
            "id_token": id_token,
            "user": user_info,
        }

    except Exception as e:
        print(f"❌ Error exchanging token: {e}")
        traceback.print_exc()
        raise HTTPException(status_code=400, detail="Token exchange failed")


# === Setup Function for App ===
def setup_auth(app):
    app.include_router(router)
