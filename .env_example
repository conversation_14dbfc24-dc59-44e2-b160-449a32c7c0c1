BASE_URL=https://llmui.pride.improwised.dev
API_KEY=<your_api_key>

MOCK_QUESTION_COUNT=10
FINAL_QUESTION_COUNT=20
TOTAL_QUESTIONS_COUNT=30

# Individual difficulty level question counts
EASY_QUESTIONS_COUNT=10
INTERMEDIATE_QUESTIONS_COUNT=10
ADVANCED_QUESTIONS_COUNT=10
MODEL_ID='qwen/qwen3-30b-a3b'
PYTHONUNBUFFERED=1

# PostgreSQL database credentials
PG_USER=
PG_PASSWORD=
PG_DATABASE=db
PG_HOST=
PG_PORT=5432

## Auth Client Configuration
AUTH_CLIENT_ID=example-app
AUTH_CLIENT_SECRET="secret-key"

# Dex
AUTH_ISSUER=http://127.0.0.1:5556/dex
AUTH_TOKEN_URL=http://127.0.0.1:5556/dex/token
AUTH_AUTHORIZE_URL=http://127.0.0.1:5556/dex/auth
AUTH_LOGOUT_URL=http://127.0.0.1:5556/dex/logout
AUTH_REDIRECT_URI=http://localhost:5173/callback
AUTH_JWKS_URI=http://127.0.0.1:5556/dex/keys

# Application URLs
BACKEND_URL=http://localhost:8000
FRONTEND_URL=http://localhost:5173
