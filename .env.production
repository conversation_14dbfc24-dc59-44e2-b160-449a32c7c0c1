BASE_URL=https://llmui.pride.improwised.dev
SERVER_PORT=8000

API_KEY=<your_api_key>
MOCK_QUESTION_COUNT=10
FINAL_QUESTION_COUNT=20
TOTAL_QUESTIONS_COUNT=30

# Individual difficulty level question counts
EASY_QUESTIONS_COUNT=10
INTERMEDIATE_QUESTIONS_COUNT=10
ADVANCED_QUESTIONS_COUNT=10
MODEL_ID='qwen/qwen3-30b-a3b'
PYTHONUNBUFFERED=1

# PostgreSQL database credentials
PG_USER=
PG_PASSWORD=
PG_DATABASE=db
PG_HOST=
PG_PORT=5432

## Auth Client Configuration
AUTH_CLIENT_ID=herbit-dev
AUTH_CLIENT_SECRET="secret-key"

# Dex
AUTH_ISSUER=https://dex.improwised.dev
AUTH_TOKEN_URL=https://dex.improwised.dev/token
AUTH_AUTHORIZE_URL=https://dex.improwised.dev/auth
AUTH_LOGOUT_URL=https://dex.improwised.dev/logout
AUTH_REDIRECT_URI=https://herbit-dev.pride.improwised.dev/callback
AUTH_JWKS_URI=https://dex.improwised.dev/keys

# Application URLs
BACKEND_URL=https://herbit-dev.pride.improwised.dev/api
FRONTEND_URL=https://herbit-dev.pride.improwised.dev
