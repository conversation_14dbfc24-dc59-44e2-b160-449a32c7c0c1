<template>
  <section class="relative pt-32 pb-20 overflow-hidden">
    <!-- Animated particles -->
    <div class="absolute inset-0 z-0">
      <div v-for="i in 30" :key="`particle-${i}`"
           class="absolute rounded-full"
           :class="[
             i % 5 === 0 ? 'bg-phantom-blue/30' : '',
             i % 5 === 1 ? 'bg-phantom-indigo/30' : '',
             i % 5 === 2 ? 'bg-phantom-purple/30' : '',
             i % 5 === 3 ? 'bg-blue-500/30' : '',
             i % 5 === 4 ? 'bg-indigo-500/30' : ''
           ]"
           :style="{
             width: `${Math.random() * 6 + 2}px`,
             height: `${Math.random() * 6 + 2}px`,
             top: `${Math.random() * 100}%`,
             left: `${Math.random() * 100}%`,
             animation: `float-particle ${Math.random() * 20 + 10}s linear infinite`,
             animationDelay: `${Math.random() * 5}s`,
             opacity: Math.random() * 0.5 + 0.2
           }"></div>
    </div>

    <div class="max-w-7xl mx-auto px-6 md:px-10 relative z-10">
      <div class="text-center max-w-3xl mx-auto">
        <!-- Main heading with animated typing effect -->
        <h1 class="text-4xl md:text-3xl font-bold text-white mb-6 leading-tight">
          Highly Engineered
          <span class="bg-gradient-to-r from-phantom-blue to-phantom-purple bg-clip-text text-transparent">
            Robot
          </span>
          Built for Internal Tests
        </h1>

        <!-- Animated HERBIT Art -->
        <AnimatedHerbitArt />


      </div>
    </div>
  </section>
</template>

<script setup>
import AnimatedHerbitArt from '@/components/home/<USER>'
</script>
