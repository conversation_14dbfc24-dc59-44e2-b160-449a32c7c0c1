<script setup>
import { SelectValue } from 'reka-ui';
import { cn } from '@/lib/utils';

const props = defineProps({
  placeholder: { type: String, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: String, required: false },
  id: { type: String, required: false },
  variant: { type: String, default: 'default' },
});
</script>

<template>
  <SelectValue v-bind="props" :class="cn(
      'text-white',
      {
        'text-sm': props.variant === 'default',
        'text-sm text-gray-400': props.variant === 'small'
      },
      props.class
    )">
    <slot />
  </SelectValue>
</template>
