<script setup>
import { cn } from '@/lib/utils';
import { Label } from 'reka-ui';
import { computed } from 'vue';

const props = defineProps({
  for: { type: String, required: false },
  asChild: { type: Boolean, required: false },
  as: { type: null, required: false },
  class: { type: null, required: false },
});

// Generate a unique ID for the "for" attribute if none is provided
const uniqueId = computed(() => `label-${Math.random().toString(36).substring(2, 11)}`);
const forId = computed(() => props.for || uniqueId.value);

const delegatedProps = computed(() => {
  const { class: _, ...delegated } = props;

  // Always include a "for" attribute
  return {
    ...delegated,
    for: forId.value
  };
});
</script>

<template>
  <Label
    v-bind="delegatedProps"
    :class="
      cn(
        'text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70',
        props.class,
      )
    "
  >
    <slot />
  </Label>
</template>
