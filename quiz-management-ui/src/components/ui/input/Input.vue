<script setup>
import { cn } from '@/lib/utils';
import { useVModel } from '@vueuse/core';
import { computed } from 'vue';

const props = defineProps({
  defaultValue: { type: [String, Number], required: false },
  modelValue: { type: [String, Number], required: false },
  class: { type: null, required: false },
  label: { type: String, required: false },
  id: { type: String, required: false },
  name: { type: String, required: false },
  autocomplete: { type: String, required: false },
  type: { type: String, default: 'text' },
  placeholder: { type: String, required: false },
  required: { type: Boolean, default: false },
  disabled: { type: Boolean, default: false },
});

const emits = defineEmits(['update:modelValue']);

const modelValue = useVModel(props, 'modelValue', emits, {
  passive: true,
  defaultValue: props.defaultValue,
});

// Generate a unique ID for this input if none is provided
const uniqueId = computed(() => `input-${Math.random().toString(36).substring(2, 11)}`);
const inputId = computed(() => props.id || uniqueId.value);
const inputName = computed(() => props.name || props.id || uniqueId.value);
</script>

<template>
  <div class="w-full">
    <!-- Render label if provided -->
    <label v-if="label" :for="inputId" class="block text-sm font-medium text-cyan-300 mb-1">
      {{ label }}
    </label>
    <input
      v-model="modelValue"
      :id="inputId"
      :name="inputName"
      :type="type"
      :placeholder="placeholder"
      :required="required"
      :disabled="disabled"
      :autocomplete="autocomplete"
      :class="
        cn(
          'flex h-9 w-full rounded-md border border-gray-600 bg-gray-700 px-3 py-1 text-sm text-gray-200 shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-cyan-500 hover:border-cyan-400 disabled:cursor-not-allowed disabled:opacity-50',
          props.class,
        )
      "
    />
  </div>
</template>

<style scoped>

input:hover {
  border-color: #22d3ee; /* cyan-400 */
}


input:focus-visible {
  border-color: #06b6d4; /* cyan-500 */
  box-shadow: 0 0 0 2px rgba(6, 182, 212, 0.5);
}


input:disabled {
  background-color: #374151; /* gray-700 */
  cursor: not-allowed;
  opacity: 0.6;
}

input::placeholder {
  color: #9ca3af; /* gray-400 */
}
</style>
