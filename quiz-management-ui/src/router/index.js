import { createRouter, createWebHistory } from 'vue-router'
import DynamicHome from '@/views/DynamicHome.vue'

const routes = [
  {
    path: '/',
    name: 'Home',
    component: DynamicHome,
    meta: { requiresAuth: true }
  },

  // Auth routes
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { requiresAuth: false }
  },
  {
    path: '/callback',
    name: 'Callback',
    component: () => import('@/views/Callback.vue'),
    meta: { requiresAuth: false }
  },

  // Admin routes
  { path: '/create-assessment', name: 'CreateAssessment', component: () => import('@/views/CreateAssessment.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/list-assessments', name: 'ListAssessments', component: () => import('@/views/ListAssessments.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/assessment/:id', name: 'AssessmentDetail', component: () => import('@/views/AssessmentDetail.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/sessions', name: 'SessionsList', component: () => import('@/views/SessionList.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/session/:sessionId', name: 'SessionDetail', component: () => import('@/views/SessionDetail.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/generate-sessions', name: 'GenerateSessions', component: () => import('@/views/Sessions.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/create-skill', name: 'CreateSkill', component: () => import('@/views/CreateSkill.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/list-skills', name: 'ListSkills', component: () => import('@/views/ListSkills.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/skill/:id', name: 'SkillDetail', component: () => import('@/views/SkillDetail.vue'), meta: { requiresAuth: true, requiresAdmin: true } },
  { path: '/report-generate', name: 'ReportGenerate', component: () => import('@/views/ReportGenerate.vue'), meta: { requiresAuth: true, requiresAdmin: true } },

  // User routes
  { path: '/user-home', name: 'UserHome', component: () => import('@/views/UserHome.vue'), meta: { requiresAuth: true } },
  { path: '/report', name: 'UserReports', component: () => import('@/views/UserReport.vue'), meta: { requiresAuth: true }},
  { path: '/user-sessions', name: 'UserSessions', component: () => import('@/views/UserSessions.vue'), meta: { requiresAuth: true }},

  // Quiz taking routes - public access for users to take quizzes
  { path: '/quiz/:sessionCode', name: 'QuizWithSession', component: () => import('@/views/TakeQuiz.vue'), meta: { requiresAuth: true } },

  { path: '/:pathMatch(.*)*', redirect: '/' },
]

const router = createRouter({
  history: createWebHistory(),
  routes,
})

// Add global navigation guards for error handling
router.onError((error) => {
  console.error('Router error:', error);
});

// Add before each guard to handle authentication and navigation issues
router.beforeEach((to, _from, next) => {
  try {
    // Public routes that don't require authentication
    // const publicRoutes = ['/login', '/callback', '/error'];

    // // Check if this is a take-quiz route (public access)
    // const isTakeQuizRoute = to.path.startsWith('/take-quiz/');

    // Check if the route exists
    if (!to.matched.length) {
      console.warn('Route not found:', to.path);
      next('/login');
      return;
    }

    // Check if user is authenticated for protected routes
    const isAuthenticated = !!localStorage.getItem('access_token');

    if (to.meta.requiresAuth && !isAuthenticated) {
      // If route requires authentication and user is not authenticated
      console.log('Authentication required, redirecting to login');
      next('/login');
      return;
    }

    // If user is authenticated and trying to access login page, redirect to home
    if (to.path === '/login' && isAuthenticated) {
      console.log('User is already authenticated, redirecting to home');
      next('/');
      return;
    }

    // Check admin-only routes
    if (to.meta.requiresAdmin && isAuthenticated) {
      const userInfoStr = localStorage.getItem('user_info');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          if (userInfo.groups) {
            console.log('User groups array:', JSON.stringify(userInfo.groups));
            console.log('Is admins in groups?', userInfo.groups.includes('admins'));

            // If user doesn't have admin group, redirect to home
            if (!userInfo.groups.includes('admins')) {
              console.log('Non-admin user trying to access admin route, redirecting to home');
              next('/');
              return;
            }
          } else {
            // No groups, redirect to home
            next('/');
            return;
          }
        } catch (e) {
          console.error('Error parsing user info:', e);
          next('/');
          return;
        }
      } else {
        // No user info, redirect to home
        next('/');
        return;
      }
    }

    next();
  } catch (error) {
    console.error('Navigation error:', error);
    next('/login');
  }
});

export default router
