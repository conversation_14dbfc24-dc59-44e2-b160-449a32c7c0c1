import { createApp } from 'vue'
import App from './App.vue'
import router from './router'
import './assets/css/main.css'
import './assets/css/theme.css'

import '@fontsource/fira-code/400.css'
import '@fontsource/fira-code/700.css'

// Import ApexCharts
import VueApexCharts from 'vue3-apexcharts'

// Import SVG Icon component
import SvgIcon from './components/SvgIcon.vue'

// Global error handler for uncaught errors
window.addEventListener('error', (event) => {
  console.error('Global error caught:', event.error);
});

// Global error handler for unhandled promise rejections
window.addEventListener('unhandledrejection', (event) => {
  console.error('Unhandled promise rejection:', event.reason);
});

try {
  const app = createApp(App);

  // Add global error handler for Vue errors
  app.config.errorHandler = (err, instance, info) => {
    console.error('Vue error caught:', err, info);
  };

  app.use(router);
  app.use(VueApexCharts);

  // Register SvgIcon component globally
  app.component('SvgIcon', SvgIcon);

  // Ensure DOM is ready before mounting
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      const appElement = document.getElementById('app');
      if (appElement) {
        app.mount('#app');
      } else {
        console.error('App element not found in DOM');
      }
    });
  } else {
    const appElement = document.getElementById('app');
    if (appElement) {
      app.mount('#app');
    } else {
      console.error('App element not found in DOM');
    }
  }
} catch (error) {
  console.error('Failed to initialize Vue app:', error);
}
