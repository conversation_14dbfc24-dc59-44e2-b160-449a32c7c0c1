/**
 * Utility functions for handling API errors
 */

/**
 * Extract a user-friendly error message from an API error
 * @param {Error} error - The error object from axios
 * @param {string} defaultMessage - Default message to show if no specific error is found
 * @returns {string} A user-friendly error message
 */
export const getErrorMessage = (error, defaultMessage = 'An error occurred. Please try again.') => {
  if (!error) return defaultMessage;

  // If it's an axios error with a response
  if (error.response) {
    // Get the error detail from the response data
    const detail = error.response.data?.detail ||
                  error.response.data?.message ||
                  error.response.data?.error;

    if (detail) return detail;

    // Handle specific status codes
    switch (error.response.status) {
      case 400:
        return 'Invalid request. Please check your input.';
      case 401:
        return 'You need to be logged in to perform this action.';
      case 403:
        return 'You don\'t have permission to perform this action.';
      case 404:
        return 'The requested resource was not found.';
      case 500:
        return 'Server error. Please try again later.';
      default:
        return `Error ${error.response.status}: ${defaultMessage}`;
    }
  }

  // Network error
  if (error.request && !error.response) {
    return 'Network error. Please check your connection and try again.';
  }

  // Other errors
  return error.message || defaultMessage;
};

/**
 * Log an error to the console with additional context
 * @param {Error} error - The error object
 * @param {string} context - Context information about where the error occurred
 */
export const logError = (error, context = '') => {
  if (context) {
    console.error(`Error in ${context}:`, error);
  } else {
    console.error('Error:', error);
  }

  // You could add additional logging here, like sending to a monitoring service
};
