/**
 * Utility functions for working with hashids in the frontend
 */

import Hashids from 'hashids';

/**
 * Create a hashids instance with the same configuration as the backend
 * @returns {Hashids} Hashids instance
 */
const getHashidsInstance = () => {
  // Use the exact same salt as the backend (SHA256 hash of "herbit_quiz_system" first 16 chars)
  const saltHash = "e0c5528cd29527d8"; // This must match backend hashid_utils.py
  const minLength = 8;

  return new Hashids(
    saltHash,
    minLength,
    "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ1234567890"
  );
};

/**
 * Decode a session hash to get the original session code
 * @param {string} hash - The hashed session code
 * @returns {string|null} The original 6-digit session code or null if decoding fails
 */
export const decodeSessionHash = (hash) => {
  if (!hash) return null;

  try {
    const hashids = getHashidsInstance();
    const decoded = hashids.decode(hash);

    if (decoded && decoded.length >= 2) {
      const [sessionCodeInt, typeNumber] = decoded;

      // Check if this is a session hash (type 3)
      if (typeNumber === 3) {
        // Convert back to 6-digit string with leading zeros
        return String(sessionCodeInt).padStart(6, '0');
      }
    }

    return null;
  } catch (error) {
    console.error('Error decoding session hash:', error);
    return null;
  }
};

/**
 * Get a displayable session code from either id_hash or session_code
 * @param {Object} session - The session object
 * @returns {string} The session code to display
 */
export const getDisplayableSessionCode = (session) => {
  if (!session) return 'N/A';

  // If we have the original session_code, use it
  if (session.session_code) {
    return session.session_code;
  }

  // If we have id_hash, try to decode it
  if (session.id_hash) {
    const decodedCode = decodeSessionHash(session.id_hash);
    if (decodedCode) {
      return decodedCode;
    }

    // If decoding fails, just show the hash
    return session.id_hash;
  }

  return 'N/A';
};
