<template>
  <div class="min-h-screen flex items-center justify-center">
    <div class="loading-message text-center p-8 bg-gray-900/70 backdrop-blur-md rounded-xl border border-cyan-500/30 shadow-glow-md">
      <div class="flex flex-col items-center">
        <!-- Loading spinner -->
        <svg class="animate-spin h-10 w-10 text-cyan-500 mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
          <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
        <h2 class="text-xl font-bold bg-gradient-to-r from-cyan-400 to-blue-500 bg-clip-text text-transparent mb-2">
          HERBIT
        </h2>
        <p class="text-gray-300">Processing authentication...</p>
        <p class="text-gray-400 text-sm mt-2">You will be redirected shortly</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

onMounted(async () => {
  const params = new URLSearchParams(window.location.search)
  const code = params.get('code')
  const receivedState = params.get('state')
  const storedState = localStorage.getItem('oauth_state')

  console.log('Received state:', receivedState)
  console.log('Stored state:', storedState)

  // Clear the stored state to prevent reuse
  localStorage.removeItem('oauth_state')

  if (!code) {
    console.warn('No code in URL, redirecting to login')
    router.push('/login')
    return
  }

  // Verify state parameter to prevent CSRF attacks
  if (!receivedState || receivedState !== storedState) {
    console.warn('State parameter mismatch or missing')
    console.warn('Received state:', receivedState)
    console.warn('Stored state:', storedState)
    // We'll continue anyway since we're using a direct token exchange on the backend
    // that doesn't rely on the state parameter for security
  }

  const data = {
    code: code,
    redirect_uri: import.meta.env.VITE_AUTH_CALLBACK_URL,
    state: receivedState // Include the state in the token request
  }

  try {
    const apiBaseUrl = import.meta.env.VITE_API_BASE_URL;
    const response = await fetch(`${apiBaseUrl}/auth/token`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify(data),
      credentials: 'include' // Include cookies for session handling
    })

    const result = await response.json()

    if (response.ok) {
      // Store the access token
      localStorage.setItem('access_token', result.access_token)

      // Store the user info directly from the token response
      if (result.user) {
        localStorage.setItem('user_info', JSON.stringify(result.user))
        console.log('User info stored:', result.user)
      } else {
        // If user info is not in the token response, fetch it separately
        try {
          const userInfoUrl = import.meta.env.VITE_AUTH_USERINFO_URL;
          const userInfoResponse = await fetch(userInfoUrl, {
            method: 'GET',
            credentials: 'include',
            headers: {
              'Authorization': `Bearer ${result.access_token}`
            }
          });

          if (userInfoResponse.ok) {
            const userInfoData = await userInfoResponse.json();
            if (userInfoData.authenticated && userInfoData.user) {
              localStorage.setItem('user_info', JSON.stringify(userInfoData.user));
              console.log('User info fetched separately:', userInfoData.user)
            }
          }
        } catch (userInfoError) {
          console.error('Error fetching user info:', userInfoError);
        }
      }

      // Log user groups for debugging
      const userInfoStr = localStorage.getItem('user_info');
      if (userInfoStr) {
        try {
          const userInfo = JSON.parse(userInfoStr);
          console.log('User info in callback:', userInfo);

          if (userInfo.groups) {
            // Get group names from environment variables
            const adminGroupName = import.meta.env.VITE_ADMIN_GROUP_NAME || 'admins';
            const employeeGroupName = import.meta.env.VITE_EMPLOYEE_GROUP_NAME || 'employees';

            console.log('User groups array:', JSON.stringify(userInfo.groups));
            console.log(`Is ${adminGroupName} in groups?`, userInfo.groups.includes(adminGroupName));
            console.log(`Is ${employeeGroupName} in groups?`, userInfo.groups.includes(employeeGroupName));
          }
        } catch (e) {
          console.error('Error parsing user info:', e);
        }
      }

      // Trigger a localStorage event to notify other components about the authentication change
      // This is needed because the storage event only fires when localStorage is changed from another tab
      window.dispatchEvent(new Event('auth-state-changed'));

      // Redirect to root path - the DynamicHome component will handle showing the appropriate view
      console.log('Authentication successful, redirecting to home');
      router.push('/')
    } else {
      console.error('Error response:', result)
      router.push('/error') // Redirect to error page
    }
  } catch (err) {
    console.error('Network or server error:', err)
    router.push('/error') // Redirect to error page
  }
})
</script>

<style scoped>
.callback-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100vh;
}

.loading-message {
  text-align: center;
  font-size: 1.2rem;
}
</style>
