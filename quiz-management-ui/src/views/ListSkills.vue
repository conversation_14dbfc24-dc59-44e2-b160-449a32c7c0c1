<template>
  <PhantomLayout
    title="Skills"
    :noScroll="true"
  >
    <div class="p-6 -mt-10">

      <!-- Removed the top action bar as we'll move the button next to search -->

      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"></div>
        <span class="ml-3 text-white/80">Loading skills...</span>
      </div>

      <!-- Error message -->
      <div v-if="message && !isSuccess" class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div v-if="message && isSuccess" class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        {{ message }}
      </div>

      <!-- No skills message -->
      <div v-if="!isLoading && !message && skills.length === 0" class="text-center py-16">
        <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No skills found</h3>
        <p class="text-white/60 mb-6">Get started by creating your first skill category</p>
      </div>

      <!-- Skills table -->
      <div v-if="!isLoading && skills.length > 0">
        <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
          <div class="w-full flex flex-row items-center">
            <div class="relative flex-1 mr-3">
              <input
                type="text"
                v-model="searchQuery"
                placeholder="Search skills..."
                class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
              />
              <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
            <!-- Add Skill button moved next to search bar -->
            <button
              @click="navigateTo('/create-skill')"
              class="btn-phantom px-5 py-2.5 text-sm whitespace-nowrap"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1.5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
                </svg>
                Add Skill
              </span>
            </button>
          </div>
        </div>

        <!-- Skills table -->
        <div class="rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Name</th>
                <th class="py-4 px-6 text-white/80 font-medium">Description</th>
                <th class="py-4 px-6 text-white/80 font-medium">Questions</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(skill, index) in paginatedSkills" :key="skill.id"
                  :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                  @click="navigateToSkillDetail(skill)">
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <div class="w-10 h-10 rounded-full bg-gradient-to-br from-phantom-blue to-phantom-indigo flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                    </div>
                    <span class="font-medium text-white">{{ skill.name }}</span>
                  </div>
                </td>
                <td class="py-4 px-6 text-white/70">
                  <div class="truncate max-w-xs">
                    {{ skill.description || 'No description available' }}
                  </div>
                </td>
                <td class="py-4 px-6">
                  <div class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-phantom-blue mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    <span class="text-white/80">{{ skill.question_count || 0 }}</span>
                  </div>
                </td>
              </tr>
            </tbody>
          </table>

          <!-- Pagination Controls -->
          <div v-if="displayTotalPages > 1" class="flex justify-center items-center mt-6 space-x-2">
            <!-- Previous Page Button -->
            <button
              @click="prevPage"
              :disabled="currentPage === 1 || displayTotalPages <= 1"
              class="px-3 py-2 rounded-lg bg-white/5 hover:bg-white/10 text-white/80 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
              </svg>
            </button>

            <!-- Page Numbers -->
            <div class="flex space-x-1">
              <button
                v-for="page in displayTotalPages"
                :key="page"
                @click="goToPage(page)"
                :disabled="displayTotalPages <= 1"
                :class="[
                  'px-3 py-1 rounded-lg transition-colors',
                  currentPage === page
                    ? 'bg-phantom-blue text-white'
                    : 'bg-white/5 hover:bg-white/10 text-white/80',
                  displayTotalPages <= 1 ? 'opacity-50 cursor-not-allowed' : ''
                ]"
              >
                {{ page }}
              </button>
            </div>

            <!-- Next Page Button -->
            <button
              @click="nextPage"
              :disabled="currentPage === displayTotalPages || displayTotalPages <= 1"
              class="px-3 py-2 rounded-lg bg-white/5 hover:bg-white/10 text-white/80 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
            >
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
              </svg>
            </button>
          </div>

          <div v-if="filteredSkills.length > 0" class="text-center mt-4 text-white/60 text-sm">
            <span v-if="!isSearching">
              Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, totalItems) }} of {{ totalItems }} skills
            </span>
            <span v-else>
              Showing {{ (currentPage - 1) * itemsPerPage + 1 }} to {{ Math.min(currentPage * itemsPerPage, filteredSkills.length) }} of {{ filteredSkills.length }} skills (filtered)
            </span>
            <span class="text-xs text-white/40 ml-2">
              (Page {{ currentPage }} of {{ displayTotalPages }})
            </span>
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { getSkillHashId } from '@/utils/hashIds';
import { extractResponseData, extractErrorInfo, extractPaginationMeta } from '@/utils/apiResponseHandler';

import PhantomLayout from '@/components/layout/Layout.vue';

const router = useRouter();
const navigateTo = (path) => {
  router.push(path);
};

// Navigate to skill detail page
const navigateToSkillDetail = (skill) => {
  const hashId = getSkillHashId(skill);
  router.push(`/skill/${hashId}`);
};

// No longer need expanded descriptions functionality since we're using truncate

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Data
const skills = ref([]);
const allSkills = ref([]); // For search functionality
const isLoading = ref(true);
const searchQuery = ref('');

// Pagination - Server-side pagination
const currentPage = ref(1);
const itemsPerPage = 7; // Exactly 7 items per page as required
const totalItems = ref(0);
const totalPages = ref(0);
const isSearching = ref(false);

// Computed properties for display
const filteredSkills = computed(() => {
  if (!isSearching.value) {
    // When not searching, use server-side paginated data
    return skills.value;
  }

  // When searching, filter all skills client-side
  if (!searchQuery.value) return allSkills.value;

  const query = searchQuery.value.toLowerCase();
  return allSkills.value.filter(skill =>
    skill.name.toLowerCase().includes(query) ||
    (skill.description && skill.description.toLowerCase().includes(query))
  );
});

const paginatedSkills = computed(() => {
  if (!isSearching.value) {
    // Server-side pagination - skills are already paginated
    return skills.value;
  }

  // Client-side pagination for search results
  const startIndex = (currentPage.value - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  return filteredSkills.value.slice(startIndex, endIndex);
});

// Update totalPages computation
const displayTotalPages = computed(() => {
  if (!isSearching.value) {
    return totalPages.value;
  }

  // For search results, calculate pages from filtered results
  return Math.ceil(filteredSkills.value.length / itemsPerPage);
});

const goToPage = (page) => {
  currentPage.value = page;
  if (!isSearching.value) {
    // Fetch new page from server
    fetchSkills();
  }
};

const nextPage = () => {
  const maxPages = isSearching.value ? displayTotalPages.value : totalPages.value;
  if (currentPage.value < maxPages) {
    currentPage.value++;
    if (!isSearching.value) {
      fetchSkills();
    }
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
    if (!isSearching.value) {
      fetchSkills();
    }
  }
};

// Fetch skills from API with server-side pagination
const fetchSkills = async () => {
  isLoading.value = true;
  clearMessage();

  try {
    const offset = (currentPage.value - 1) * itemsPerPage;
    const params = {
      limit: itemsPerPage,
      offset: offset
    };

    const response = await api.admin.getSkills(params);
    const skillsData = extractResponseData(response);
    const paginationMeta = extractPaginationMeta(response);

    if (skillsData) {
      // Skills data already includes question_count from backend
      skills.value = skillsData;
    }

    // Update pagination metadata from server
    if (paginationMeta) {
      totalItems.value = paginationMeta.total;
      totalPages.value = Math.ceil(paginationMeta.total / itemsPerPage);
    }

    if (!skills.value || skills.value.length === 0) {
      // No skills found or empty array returned
    }
  } catch (error) {
    logError(error, 'fetchSkills');
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || 'An unexpected error occurred while fetching skills');
  } finally {
    isLoading.value = false;
  }
};

// Fetch all skills for search functionality
const fetchAllSkills = async () => {
  try {
    // Fetch all skills (use a large limit to get all)
    const params = {
      limit: 1000, // Reasonable upper limit
      offset: 0
    };

    const response = await api.admin.getSkills(params);
    const skillsData = extractResponseData(response);

    if (skillsData) {
      // Skills data already includes question_count from backend
      allSkills.value = skillsData;
    }
  } catch (error) {
    logError(error, 'fetchAllSkills');
    console.error('Failed to fetch all skills for search:', error);
  }
};

// Note: fetchQuestionCounts function removed - question counts are now included directly in the skills API response

// Reset to first page when search query changes
watch(searchQuery, async (newQuery, oldQuery) => {
  currentPage.value = 1;

  if (newQuery && !oldQuery) {
    // Started searching - fetch all skills if not already fetched
    if (allSkills.value.length === 0) {
      await fetchAllSkills();
    }
    isSearching.value = true;
  } else if (!newQuery && oldQuery) {
    // Stopped searching - go back to server-side pagination
    isSearching.value = false;
    await fetchSkills();
  }
});

onMounted(() => {
  fetchSkills();
});
</script>
