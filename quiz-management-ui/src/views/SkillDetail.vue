<template>
    <PhantomLayout>
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-phantom-blue"></div>
        <span class="ml-4 text-white text-lg">Loading skill details...</span>
      </div>

      <!-- Message display with animation -->
      <transition name="fade">
        <div v-if="message" class="mb-6 px-6">
          <div
            :class="isSuccess ? 'bg-green-500/10 border-green-500/30' : 'bg-red-500/10 border-red-500/30'"
            class="px-6 py-4 rounded-xl border backdrop-blur-sm text-white flex items-center"
          >
            <div v-if="isSuccess" class="flex-shrink-0 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
              </svg>
            </div>
            <div v-else class="flex-shrink-0 mr-3">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
              </svg>
            </div>
            <div class="flex-grow">
              {{ message }}
            </div>
            <button @click="clearMessage()" class="flex-shrink-0 ml-2 p-1 rounded-full hover:bg-white/10 transition-colors">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>
      </transition>

      <!-- Skill Details -->
      <div v-if="skill && !isLoading" class="w-full max-w-none mx-auto px-8">
        <!-- Header with Back Button -->
        <div class="flex justify-end items-center mb-8">
          <div class="flex space-x-4">
            <button
              @click="navigateToListSkills"
              class="btn-phantom-secondary px-5 py-2.5"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Skills
              </span>
            </button>
            <button
              @click="generateQuestions"
              :disabled="isGenerating"
              class="btn-phantom px-5 py-2.5"
              :class="{ 'opacity-75 cursor-not-allowed': isGenerating }"
            >
              <span class="flex items-center">
                <svg v-if="isGenerating" class="animate-spin -ml-1 mr-2 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                  <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <svg v-else xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
                {{ isGenerating ? 'Generating... (up to 5 min)' : 'Generate Questions' }}
              </span>
            </button>
          </div>
        </div>

        <!-- Two Column Layout -->
        <div class="grid grid-cols-1 xl:grid-cols-2 gap-12">

          <!-- LEFT SIDE -->
          <div class="space-y-10">
            <!-- Skill Basic Information -->
            <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10">
              <!-- Skill Name as Title -->
              <h1 class="text-4xl font-extrabold text-white mb-8 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">
                {{ skill.name }}
              </h1>

              <!-- Description Section -->
              <div>
                <h3 class="text-white text-lg font-medium mb-6">Description</h3>
                <div class="bg-white/5 backdrop-blur-sm rounded-xl p-8 max-h-[400px] overflow-y-auto custom-scrollbar border border-white/10">
                  <div class="text-white max-w-none prose prose-invert prose-sm markdown-content" v-html="parsedDescription"></div>
                </div>
              </div>
            </section>
          </div>

          <!-- RIGHT SIDE -->
          <div class="space-y-10">
            <!-- Questions Section -->
            <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-10">
              <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">
                Questions ({{ questions.length || 0 }})
              </h2>

              <!-- No questions message -->
              <div v-if="!questions || questions.length === 0" class="text-center py-16 bg-white/5 backdrop-blur-sm rounded-xl border border-white/10">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-20 w-20 mx-auto text-white/20 mb-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                <p class="text-white text-xl">No questions found for this skill</p>
                <p class="text-white/60 mt-3 text-base">Click the "Generate Questions" button to create questions for this skill.</p>
              </div>

              <!-- Questions List -->
              <div v-else class="max-h-[600px] overflow-y-auto custom-scrollbar pr-4">
                <div class="space-y-8">
                  <div v-for="(question, index) in sortedQuestions" :key="question.que_id"
                       class="bg-white/5 backdrop-blur-sm p-6 rounded-xl border border-white/10 hover:bg-white/10 transition-colors"
                       :class="{
                         'border-l-4 border-l-green-400/70': question.level === 'easy',
                         'border-l-4 border-l-yellow-400/70': question.level === 'intermediate',
                         'border-l-4 border-l-orange-400/70': question.level === 'advanced'
                       }">
                    <div class="flex items-start justify-between mb-4">
                      <div class="flex items-center space-x-3">
                        <span class="bg-white/10 text-white text-xs font-medium px-3 py-1 rounded-full">{{ index + 1 }}</span>
                      </div>
                    </div>

                    <!-- Question Text -->
                    <h3 class="text-white text-base font-medium mb-4 leading-relaxed">{{ question.question }}</h3>

                    <!-- Options -->
                    <div class="space-y-2 mb-4">
                      <div v-for="(option, key) in question.options" :key="key"
                           class="flex items-start space-x-3 p-3 rounded-lg"
                           :class="{
                             'bg-green-500/10 border border-green-500/30': key.toLowerCase() === question.answer.toLowerCase(),
                             'bg-white/5 border border-white/10': key.toLowerCase() !== question.answer.toLowerCase()
                           }">
                        <span class="text-white/80 font-medium text-sm min-w-[20px]">{{ key.toUpperCase() }}.</span>
                        <span class="text-white/90 text-sm leading-relaxed">{{ option }}</span>
                        <svg v-if="key.toLowerCase() === question.answer.toLowerCase()"
                             xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-green-400 flex-shrink-0 mt-0.5 ml-auto"
                             fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </section>
          </div>
        </div>
      </div>
    </PhantomLayout>
  </template>


  <script setup>
  import { ref, onMounted, computed } from 'vue';
  import { useRoute, useRouter } from 'vue-router';
  import { api } from '@/services/api';
  import { getErrorMessage, logError } from '@/utils/errorHandling';
  import { useMessageHandler } from '@/utils/messageHandler';
  import PhantomLayout from '@/components/layout/Layout.vue';
  import { marked } from 'marked';
  import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';

  const route = useRoute();
  const router = useRouter();

  // Message handling
  const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

  const skillId = ref(null);
  const skill = ref(null);
  const questions = ref([]);
  const isLoading = ref(true);
  const isGenerating = ref(false);

  // Parse the skill description as markdown
  const parsedDescription = computed(() => {
    if (!skill.value || !skill.value.description) return '';

    // Configure marked options
    marked.setOptions({
      breaks: true,        // Add line breaks on single line breaks
      gfm: true,           // GitHub Flavored Markdown
      headerIds: true,     // Include IDs in headings
      mangle: false,       // Don't escape HTML
      smartLists: true,    // Use smarter list behavior
      smartypants: true,   // Use "smart" typographic punctuation
    });

    // Use marked to parse markdown to HTML
    return marked(skill.value.description);
  });

  // Sort questions by difficulty level: easy, intermediate, advanced
  const sortedQuestions = computed(() => {
    if (!questions.value || questions.value.length === 0) return [];

    // Create a copy of the questions array to avoid modifying the original
    return [...questions.value].sort((a, b) => {
      const difficultyOrder = { 'easy': 1, 'intermediate': 2, 'advanced': 3 };
      return difficultyOrder[a.level] - difficultyOrder[b.level];
    });
  });



  // Navigate back to the skills list
  const navigateToListSkills = () => {
    router.push('/list-skills');
  };

  // Fetch skill details and questions
  const fetchSkillDetails = async () => {
    isLoading.value = true;
    clearMessage();
    skillId.value = route.params.id;

    try {
      // Validate skill ID
      if (!skillId.value) {
        console.error('Skill ID is missing in route params');
        setErrorMessage('Skill ID is missing. Please return to the skills list and try again.');
        isLoading.value = false;
        return;
      }



      // Fetch skill details using the hash ID directly
      const skillResponse = await api.admin.getSkill(skillId.value);

      // Extract data using our utility function
      const data = extractResponseData(skillResponse);

      // Validate response
      if (!data) {
        console.error('Invalid response format from server', skillResponse);
        throw new Error('Invalid response format from server');
      }

      // Set the skill data
      skill.value = data;


      // Additional validation - check for either id or id_hash
      if (!skill.value || (!skill.value.id && !skill.value.id_hash)) {
        console.error('Skill data is invalid or missing ID', skill.value);
        setErrorMessage(`Skill with ID ${skillId.value} not found or has invalid data. Please check the URL or return to the skills list.`);
        isLoading.value = false;
        return;
      }



      // Fetch questions for this skill
      await fetchSkillQuestions();
    } catch (error) {
      logError(error, 'fetchSkillDetails');

      // Handle specific error cases
      if (error.response && error.response.status === 404) {
        setErrorMessage(`Skill with ID ${skillId.value} not found. Please check the URL or return to the skills list.`);
      } else if (error.response && error.response.data && error.response.data.message) {
        setErrorMessage(`Error: ${error.response.data.message}`);
      } else {
        const errorInfo = extractErrorInfo(error);
        setErrorMessage(errorInfo.message || 'Failed to load skill details');
      }

      // Reset skill data to prevent using invalid data
      skill.value = null;
    } finally {
      isLoading.value = false;
    }
  };

  // Fetch questions for this skill
  const fetchSkillQuestions = async (suppressErrors = false) => {
    try {
      const response = await api.admin.getSkillQuestions(skillId.value);
      const data = extractResponseData(response);

      if (data && data.questions) {
        questions.value = data.questions;
      } else if (Array.isArray(data)) {
        // Handle case where data might be an array directly
        questions.value = data;
      } else {
        questions.value = [];
      }

      // Only clear error messages if we're not suppressing errors (i.e., during initial load)
      if (!suppressErrors) {
        clearMessage();
      }
    } catch (error) {
      if (error.response && error.response.status === 404) {
        // No questions found is not an error, just an empty state
        questions.value = [];
        // No questions found for this skill. This is normal for new skills.
      } else {
        logError(error, 'fetchSkillQuestions');
        // Only set error message if we're not suppressing errors
        if (!suppressErrors) {
          const errorInfo = extractErrorInfo(error);
          setErrorMessage(errorInfo.message || 'Failed to fetch questions');
        } else {
          // If suppressing errors, just log them but don't show to user
          console.warn('Error fetching questions (suppressed):', extractErrorInfo(error).message);
        }
      }
    }
  };

  // Generate questions for this skill
  const generateQuestions = async () => {
    isGenerating.value = true;
    clearMessage();

    // First check if skill data is loaded
    if (!skill.value) {
      console.error('Skill data is not loaded yet');
      setErrorMessage('Skill data is not loaded yet. Please refresh the page and try again.');
      isGenerating.value = false;
      return;
    }

    // Validate skill ID (could be id or id_hash depending on API response format)
    if (!skill.value.id && !skill.value.id_hash) {
      console.error('Skill ID is missing', skill.value);
      setErrorMessage('Skill ID is missing or invalid. Please refresh the page and try again.');
      isGenerating.value = false;
      return;
    }

    // Check all possible ID fields in the skill object
    const idFields = {};

    // Log all properties that might contain an ID
    for (const key of Object.keys(skill.value)) {
      if (key.includes('id') || typeof skill.value[key] === 'number') {
        idFields[key] = skill.value[key];
      }
    }



    // Get the skill ID directly from the skill object
    // The backend can handle both numeric IDs and hash IDs
    let skillId = null;

    // First, try to use the 'id' field if it exists
    if (skill.value.id !== undefined && skill.value.id !== null) {
      skillId = skill.value.id;
      console.log('Using skill.id:', skillId);
    }
    // Next, try to use the 'id_hash' field if it exists
    else if (skill.value.id_hash) {
      skillId = skill.value.id_hash;
      console.log('Using skill.id_hash:', skillId);
    }
    // Finally, try to use the route parameter
    else if (route.params.id) {
      skillId = route.params.id;
      console.log('Using route.params.id:', skillId);
    }

  ;

    // Show informative message about the generation process
    setSuccessMessage('Question generation started. This may take up to 5 minutes. Please wait...', 0); // No auto-clear

    try {
      // Additional validation for all required fields
      if (!skillId) {
        throw new Error('Skill ID is required for question generation');
      }

      if (!skill.value.name || skill.value.name.trim() === '') {
        throw new Error('Skill name is required for question generation');
      }

      if (!skill.value.description || skill.value.description.trim().length < 20) {
        throw new Error('Skill description must be at least 20 characters long for effective question generation');
      }

      // We've already tried to find the numeric ID above, just log what we're using
      console.log(`Using skillId: ${skillId} (type: ${typeof skillId})`);

      // Log the complete request data for debugging
      const requestData = {
        skillId: skillId,
        skillName: skill.value.name,
        skillDescription: skill.value.description
      };

      console.log('Sending question generation request with data:', requestData);

      const response = await api.admin.generateSkillQuestions(requestData);

      console.log('Question generation response:', response);

      // Extract data using our utility function
      const data = extractResponseData(response);

      // Validate response structure
      if (!data) {
        throw new Error('Invalid response from server');
      }

      // Check if the operation was actually successful (for legacy format)
      if (data.success === false) {
        throw new Error(data.message || 'Question generation failed');
      }

      // Show success message with count if available
      const generatedCount = data?.count || data?.questionCount || '';
      const countText = generatedCount ? ` (${generatedCount} questions)` : '';
      const successMsg = data.message || `Successfully generated questions for ${skill.value.name}${countText}`;

      // Set success message first
      setSuccessMessage(successMsg);

      // Refresh the questions list to show the new questions
      // Use suppressErrors=true to prevent refresh errors from overriding success message
      try {
        await fetchSkillQuestions(true); // Suppress errors to preserve success message
      } catch (refreshError) {
        // Log the refresh error but don't override the success message
        logError(refreshError, 'fetchSkillQuestions after generation');
        console.warn('Questions were generated successfully, but there was an issue refreshing the list:', refreshError);
        // The success message will remain visible to show that generation was successful
      }

    } catch (error) {
      logError(error, 'generateQuestions');

      // Check if this is a timeout error and provide a more helpful message
      if (error.code === 'ECONNABORTED' || (error.message && error.message.includes('timeout'))) {
        setErrorMessage('Question generation is taking longer than expected. The process may still be running in the background. Please wait a moment and refresh the page to check if questions were generated.');
      } else if (error.response && error.response.data && error.response.data.message) {
        // Extract error message from API response
        setErrorMessage(`Error: ${error.response.data.message}`);
      } else if (error.message) {
        // Use error message directly
        setErrorMessage(`Error: ${error.message}`);
      } else {
        // Use our utility function to extract error info
        const errorInfo = extractErrorInfo(error);
        setErrorMessage(errorInfo.message || 'An unexpected error occurred while generating questions');
      }
    } finally {
      isGenerating.value = false;
    }
  };

  onMounted(() => {
    fetchSkillDetails();
  });
  </script>
