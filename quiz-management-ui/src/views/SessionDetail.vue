<template>
  <PhantomLayout
    title="Session Details"
  >
    <div class="p-6">
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"></div>
        <span class="ml-3 text-white/80">Loading session details...</span>
      </div>

      <!-- Error message -->
      <div v-if="message && !isSuccess" class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        {{ message }}
      </div>

      <!-- Session details card -->
      <div v-if="!isLoading && session" class="card-phantom">
        <div class="p-6">
          <h2 class="text-xl font-semibold text-white mb-6 flex items-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2 text-phantom-blue" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
            </svg>
            Session Information
          </h2>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            <div class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Username</p>
              <p class="text-white font-medium">{{ session.username }}</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Session Code</p>
              <p class="font-mono bg-white/10 text-phantom-blue px-3 py-1 rounded-lg inline-block">{{ displaySessionCode }}</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Assessment</p>
              <p class="text-white">{{ session.assessment_name }}</p>
            </div>
            <div v-if="session.assessment_id_hash || session.assessment_id" class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Assessment ID</p>
              <p class="text-white font-mono">{{ session.assessment_id_hash || session.assessment_id }}</p>
            </div>
            <div class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Status</p>
              <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium"
                    :class="getStatusClass(session.status)">
                {{ session.status || 'Pending' }}
              </span>
            </div>
            <div v-if="session.created_at" class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Created At</p>
              <p class="text-white">{{ formatDate(session.created_at) }}</p>
            </div>
            <div v-if="session.completed_at" class="bg-white/5 backdrop-blur-sm p-4 rounded-xl border border-white/10">
              <p class="text-white/60 text-sm mb-1">Completed At</p>
              <p class="text-white">{{ formatDate(session.completed_at) }}</p>
            </div>
          </div>





          <!-- Actions -->
          <div class="flex justify-end mt-6">
            <button
              @click="navigateBack"
              class="btn-phantom-secondary px-5 py-2.5"
            >
              <span class="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 19l-7-7m0 0l7-7m-7 7h18" />
                </svg>
                Back to Sessions
              </span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import { isHashId, decodeSessionId, decodeSessionCodeFromHash } from '@/utils/hashIds';
import PhantomLayout from '@/components/layout/Layout.vue';

const route = useRoute();
const router = useRouter();
const sessionId = route.params.sessionId;

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Session data
const session = ref(null);
const isLoading = ref(false);

// Computed property for session code display
const displaySessionCode = computed(() => {
  if (!session.value) return 'N/A';

  // Try different possible session code fields
  return session.value.session_code ||
         session.value.code ||
         session.value.sessionCode ||
         'N/A';
});

// Navigate back to sessions list
const navigateBack = () => {
  router.push('/sessions');
};

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Format label for display
const formatLabel = (key) => {
  return key
    .replace(/_/g, ' ')
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};

// Get CSS class for status badge
const getStatusClass = (status) => {
  switch (status) {
    case 'completed':
      return 'bg-green-500/10 text-green-400 border border-green-500/20';
    case 'in_progress':
      return 'bg-blue-500/10 text-blue-400 border border-blue-500/20';
    case 'pending':
    case 'created':
    default:
      return 'bg-yellow-500/10 text-yellow-400 border border-yellow-500/20';
  }
};

// Fetch session details from API
const fetchSessionDetails = async () => {
  try {
    isLoading.value = true;
    clearMessage();

    console.log('Session ID from URL:', sessionId);
    console.log('Is hash ID:', isHashId(sessionId));

    let actualSessionId = sessionId;

    // If it's a hash ID, try to decode it to get the actual ID or session code
    if (isHashId(sessionId)) {
      try {
        // First try to decode to get the numeric ID
        const decodedId = await decodeSessionId(sessionId);
        if (decodedId) {
          actualSessionId = decodedId.toString();
          console.log('Decoded hash to numeric ID:', actualSessionId);
        } else {
          // If that fails, try to decode to get the 6-digit session code
          const decodedCode = await decodeSessionCodeFromHash(sessionId);
          if (decodedCode) {
            actualSessionId = decodedCode;
            console.log('Decoded hash to session code:', actualSessionId);
          } else {
            console.warn('Failed to decode hash ID, will try using as-is');
          }
        }
      } catch (decodeError) {
        console.warn('Error decoding hash ID:', decodeError);
        // Will fall back to using the original sessionId
      }
    }

    console.log('Using session ID for API call:', actualSessionId);

    // Use the new session details endpoint
    const response = await api.admin.getSessionDetails(actualSessionId);

    console.log('Full API response:', response);
    console.log('Response data:', response.data);
    console.log('Response success:', response.success);

    if (response.data.success && response.data.data) {
      session.value = response.data.data;
      console.log('Session details retrieved:', session.value);

      // If the session doesn't have a session_code, try to get it from various sources
      if (!session.value.session_code && !session.value.code) {
        // First check if sessionId itself is a 6-digit code
        if (sessionId && /^\d{6}$/.test(sessionId)) {
          session.value.session_code = sessionId;
          console.log('Using sessionId as session code:', sessionId);
        }
        // If sessionId is a hash, try to decode it
        else if (isHashId(sessionId)) {
          try {
            const decodedCode = await decodeSessionCodeFromHash(sessionId);
            if (decodedCode) {
              session.value.session_code = decodedCode;
              console.log('Decoded and set session code for display:', decodedCode);
            }
          } catch (error) {
            console.warn('Failed to decode session code for display:', error);
          }
        }
      }
    } else {
      console.log('Failed to get session details - response:', response);
      setErrorMessage(`Session with ID ${sessionId} not found`);
    }
  } catch (error) {
    logError(error, 'fetchSessionDetails');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch session details'));
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  if (sessionId) {
    fetchSessionDetails();
  } else {
    setErrorMessage('No session ID provided');
  }
});
</script>
