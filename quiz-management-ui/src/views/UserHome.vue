<template>
  <PhantomLayout :no-scroll="true">
    <!-- Main Content -->
    <div class="w-full max-w-7xl mx-auto h-full overflow-hidden">


      <!-- Quick Actions -->
      <div class="mb-6">
        <div class="flex flex-wrap gap-4">
          <button class="btn-phantom px-5 py-2.5 text-sm">
            <span class="flex items-center">
              <SvgIcon name="document" class="mr-2" />
              My Assessments
            </span>
          </button>
          <button class="btn-phantom-secondary px-5 py-2.5 text-sm">
            <span class="flex items-center">
              <SvgIcon name="chart-bar" class="mr-2" />
              View Progress
            </span>
          </button>
        </div>
      </div>

      <!-- Dashboard Cards -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        <!-- Upcoming Assessments Card -->
        <div class="bg-white/5 backdrop-blur-sm border border-white/10 p-4 rounded-xl">
          <div class="flex items-center mb-4">
            <div class="p-3 bg-phantom-blue/10 rounded-full mr-4">
              <SvgIcon name="calendar" size="lg" class="text-phantom-blue" />
            </div>
            <h3 class="text-xl font-semibold text-white">Upcoming Assessments</h3>
          </div>
          <p class="text-white/70 mb-4">You have no upcoming assessments scheduled.</p>
          <button class="text-phantom-blue font-medium hover:text-phantom-blue/80 transition-colors">View all</button>
        </div>

        <!-- Recent Results Card -->
        <div class="bg-white/5 backdrop-blur-sm border border-white/10 p-4 rounded-xl">
          <div class="flex items-center mb-4">
            <div class="p-3 bg-phantom-purple/10 rounded-full mr-4">
              <SvgIcon name="chart-bar" size="lg" class="text-phantom-purple" />
            </div>
            <h3 class="text-xl font-semibold text-white">Recent Results</h3>
          </div>
          <p class="text-white/70 mb-4">No recent assessment results to display.</p>
          <button class="text-phantom-purple font-medium hover:text-phantom-purple/80 transition-colors">View history</button>
        </div>

        <!-- Skills Progress Card -->
        <div class="bg-white/5 backdrop-blur-sm border border-white/10 p-4 rounded-xl">
          <div class="flex items-center mb-4">
            <div class="p-3 bg-phantom-indigo/10 rounded-full mr-4">
              <SvgIcon name="lightning-bolt" size="lg" class="text-phantom-indigo" />
            </div>
            <h3 class="text-xl font-semibold text-white">Skills Progress</h3>
          </div>
          <p class="text-white/70 mb-4">Track your skill development and progress.</p>
          <button class="text-phantom-indigo font-medium hover:text-phantom-indigo/80 transition-colors">View skills</button>
        </div>
      </div>

      <!-- Recent Activity Section -->
      <div class="mt-6">
        <h2 class="text-xl font-bold text-white mb-4">Recent Activity</h2>
        <div class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-4">
          <div class="text-center py-4">
            <SvgIcon name="clock" size="3xl" class="mx-auto text-white/30 mb-4" />
            <p class="text-white/70">No recent activity to display</p>
            <p class="text-white/50 text-sm mt-2">Your recent assessment activities will appear here</p>
          </div>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue';
import PhantomLayout from '../components/layout/Layout.vue';

// Store user info
const userInfo = ref(null);

// Computed property to check if user is an admin
const isAdmin = computed(() => {
  return userInfo.value &&
         userInfo.value.groups &&
         userInfo.value.groups.includes('admins');
});

onMounted(() => {
  console.log('PhantomUserHome component mounted - Employee view');

  // Disable page scrolling
  document.body.style.overflow = 'hidden';
  document.documentElement.style.overflow = 'hidden';

  // Get user info for determining permissions
  const userInfoStr = localStorage.getItem('user_info');
  if (userInfoStr) {
    try {
      userInfo.value = JSON.parse(userInfoStr);
      console.log('User info in PhantomUserHome:', userInfo.value);
      console.log('User groups:', userInfo.value.groups);
      console.log('Is admin:', isAdmin.value);
    } catch (e) {
      console.error('Error parsing user info:', e);
    }
  }
});

// Re-enable page scrolling when component unmounts
onUnmounted(() => {
  document.body.style.overflow = '';
  document.documentElement.style.overflow = '';
});
</script>

<style scoped>
/* Using common CSS from views-common.css */
.h-full {
  height: 100%;
}

/* Ensure content fits within viewport */
:deep(.phantom-layout) {
  height: 100vh;
  overflow: hidden;
}
</style>
