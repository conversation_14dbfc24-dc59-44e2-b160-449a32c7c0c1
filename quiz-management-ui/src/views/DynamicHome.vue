<template>
  <div>
    <!-- Show admin home if user has admin group -->
    <Home v-if="showAdminHome" />

    <!-- Show user home if user has employee group -->
    <UserHome v-if="showUserHome" />

    <!-- Show message if user has no relevant groups -->
    <div v-if="!showAdminHome && !showUserHome" class="min-h-screen flex items-center justify-center">
      <div class="text-center p-8 bg-gray-100 rounded-lg shadow-md">
        <h1 class="text-2xl font-bold text-gray-800 mb-4">Access Restricted</h1>
        <p class="text-gray-600">You don't have permission to access this application.</p>
        <p class="text-gray-600 mt-2">Please contact your administrator for assistance.</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import Home from './Home.vue';
import UserHome from './UserHome.vue';

// Track which components to show
const showAdminHome = ref(false);
const showUserHome = ref(false);

onMounted(() => {
  // Get group names from environment variables
  const adminGroupName = import.meta.env.VITE_ADMIN_GROUP_NAME || 'admins';
  const employeeGroupName = import.meta.env.VITE_EMPLOYEE_GROUP_NAME || 'employees';

  // Determine which components to show based on user groups
  const userInfoStr = localStorage.getItem('user_info');
  if (userInfoStr) {
    try {
      const userInfo = JSON.parse(userInfoStr);
      console.log('User info in DynamicHome:', userInfo);

      if (userInfo.groups) {
        console.log('User groups array:', JSON.stringify(userInfo.groups));

        const hasAdminGroup = userInfo.groups.includes(adminGroupName);
        const hasEmployeeGroup = userInfo.groups.includes(employeeGroupName);

        console.log(`Has ${adminGroupName} group:`, hasAdminGroup);
        console.log(`Has ${employeeGroupName} group:`, hasEmployeeGroup);

        // Prioritize admin view if user has admin group
        if (hasAdminGroup) {
          console.log('Admin user detected, showing admin home');
          showAdminHome.value = true;
          showUserHome.value = false; // Don't show both interfaces
        }
        // Only show user home if user has employee group but not admin group
        else if (hasEmployeeGroup) {
          console.log('Employee user detected, showing user home');
          showUserHome.value = true;
          showAdminHome.value = false;
        }

        // Log which components will be shown
        console.log('Showing admin home:', showAdminHome.value);
        console.log('Showing user home:', showUserHome.value);
      }
    } catch (e) {
      console.error('Error parsing user info:', e);
    }
  }
});
</script>
