<template>
  <div class="min-h-screen bg-gradient-to-br from-phantom-dark via-phantom-dark-blue to-black font-sans relative overflow-hidden">
    <!-- Phantom Background Effects -->
    <div class="absolute inset-0 overflow-hidden">
      <!-- Grid Pattern -->
      <div class="absolute inset-0 bg-grid-phantom opacity-20"></div>

      <!-- Floating Particles -->
      <div v-for="i in 30" :key="i"
           class="absolute rounded-full"
           :class="[
             i % 3 === 0 ? 'bg-phantom-blue/20 animate-float-slow' : '',
             i % 3 === 1 ? 'bg-phantom-indigo/20 animate-float-slow-reverse' : '',
             i % 3 === 2 ? 'bg-phantom-purple/20 animate-pulse-slow' : ''
           ]"
           :style="{
             width: `${Math.random() * 8 + 3}px`,
             height: `${Math.random() * 8 + 3}px`,
             top: `${Math.random() * 100}%`,
             left: `${Math.random() * 100}%`,
             animationDelay: `${Math.random() * 5}s`
           }"></div>

      <!-- Radial Vignette -->
      <div class="absolute inset-0 bg-radial-vignette"></div>
    </div>

    <!-- Main Content -->
    <div class="relative z-10 min-h-screen flex items-start justify-center pt-8">
      <div class="w-full max-w-5xl">
        <!-- Header -->
        <div class="text-center mb-8">
          <h2 class="text-xl font-bold text-white">{{ assessmentName }}</h2>
        </div>

        <!-- User Details Form -->
        <div v-if="!quizStarted" class="card-phantom p-8 shadow-glow-md">
          <h2 class="text-2xl font-bold text-white mb-6 text-center bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Enter Your Details</h2>

          <form @submit.prevent="startQuiz" class="space-y-6">
            <!-- Session Code Field -->
            <div>
              <label for="sessionCode" class="block text-sm font-medium text-white/80 mb-2">
                Session Code (Optional)
              </label>
              <input
                id="sessionCode"
                name="sessionCode"
                v-model="existingSessionCode"
                type="text"
                maxlength="6"
                pattern="[0-9]{6}"
                autocomplete="off"
                @input="onSessionCodeChange"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 font-mono text-center text-lg tracking-widest placeholder-white/40"
                placeholder="123456"
              />
              <p class="text-xs text-white/60 mt-1">
                If you have an existing session code, enter it here. Your username will be automatically filled.
              </p>
            </div>

            <!-- Username Field -->
            <div>
              <label for="username" class="block text-sm font-medium text-white/80 mb-2">Username</label>
              <input
                id="username"
                name="username"
                v-model="username"
                type="text"
                autocomplete="username"
                :required="!existingSessionCode"
                :disabled="isSessionCodeValid"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 disabled:opacity-50 disabled:cursor-not-allowed placeholder-white/40"
                placeholder="Enter your username"
              />
              <p v-if="isSessionCodeValid" class="text-xs text-green-400 mt-1">
                Username automatically filled from session code
              </p>
            </div>

            <!-- Email Field -->
            <div>
              <label for="email" class="block text-sm font-medium text-white/80 mb-2">Email (Optional)</label>
              <input
                id="email"
                name="email"
                v-model="email"
                type="email"
                autocomplete="email"
                class="w-full px-4 py-3 bg-white/5 border border-white/10 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 placeholder-white/40"
                placeholder="Enter your email"
              />
            </div>

            <button
              type="submit"
              :disabled="isLoading || (!username.trim() && !existingSessionCode)"
              class="btn-phantom w-full px-6 py-3 text-base"
            >
              <span>{{ isLoading ? 'Starting Quiz...' : (existingSessionCode ? 'Continue with Session' : 'Start Quiz') }}</span>
            </button>
          </form>

          <!-- Error Message -->
          <div v-if="errorMessage" class="mt-6 p-4 bg-red-500/10 border border-red-500/30 rounded-lg">
            <p class="text-red-400 text-sm">{{ errorMessage }}</p>
          </div>
        </div>

        <!-- Quiz Interface -->
        <div v-else-if="!quizCompleted" class="card-phantom p-8 shadow-glow-md">
          <!-- Quiz Header -->
          <div class="flex justify-between items-center mb-6">
            <div class="text-white">
              <h2 class="text-xl font-bold text-white">Session: <span class="text-phantom-blue font-mono">{{ sessionCode }}</span></h2>
            </div>
            <div class="text-right">
              <div class="text-white text-lg font-semibold">
                Question {{ currentQuestionIndex + 1 }} of {{ maxQuestions }}
              </div>
            </div>
          </div>

          <!-- Timer -->
          <div class="mb-6 text-center">
            <div class="text-2xl font-bold" :class="isTimerPaused ? 'text-yellow-400' : 'text-phantom-blue'">
              Time Remaining: {{ formatTime(timeRemaining) }}
              <span v-if="isTimerPaused" class="text-sm ml-2 animate-pulse">(PAUSED)</span>
            </div>
            <div class="w-full bg-white/10 rounded-full h-2 mt-2">
              <div
                class="h-2 rounded-full transition-all duration-1000"
                :class="isTimerPaused ? 'bg-gradient-to-r from-yellow-500 to-orange-500' : 'bg-gradient-to-r from-phantom-blue to-phantom-indigo'"
                :style="{ width: `${(timeRemaining / totalQuizTime) * 100}%` }"
              ></div>
            </div>
            <div v-if="isTimerPaused" class="text-yellow-400 text-sm mt-1">
              ⏸️ Timer paused - Return to this tab to resume
            </div>
          </div>

          <!-- Question Display -->
          <div v-if="currentQuestion" class="mb-1">
            <div class="mb-6">
              <div class="flex items-center mb-4">
                <span
                  class="px-3 py-1 rounded-full text-xs font-semibold mr-3"
                  :class="{
                    'bg-green-500/20 text-green-400': currentQuestion.level === 'easy',
                    'bg-yellow-500/20 text-yellow-400': currentQuestion.level === 'intermediate',
                    'bg-orange-500/20 text-orange-400': currentQuestion.level === 'advanced'
                  }"
                >
                  {{ currentQuestion.level.toUpperCase() }}
                </span>
              </div>
              <h3 class="text-xl text-white font-medium leading-relaxed">
                {{ currentQuestion.question }}
              </h3>
            </div>

            <!-- Answer Options -->
            <div class="space-y-3">
              <button
                v-for="(option, key) in currentQuestion.options"
                :key="key"
                @click="selectAnswer(key)"
                :disabled="answerSubmitted || timeRemaining <= 0"
                class="w-full p-4 text-left bg-white/5 border border-white/10 rounded-lg text-white hover:bg-white/10 hover:border-phantom-blue/50 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed"
                :class="getAnswerButtonClass(key)"
              >
                <div class="flex items-center">
                  <span class="w-8 h-8 bg-white/10 rounded-full flex items-center justify-center text-sm font-semibold mr-4"
                        :class="getAnswerIconClass(key)">
                    {{ key.toUpperCase() }}
                  </span>
                  <span>{{ option }}</span>
                </div>
              </button>
            </div>

            <!-- Submit Button -->
            <div class="mt-6 flex justify-end items-center gap-4">
              <button
                v-if="selectedAnswer && !answerSubmitted"
                @click="submitCurrentAnswer"
                :disabled="!selectedAnswer || answerSubmitted || timeRemaining <= 0"
                class="btn-phantom px-6 py-2"
              >
                <span>Submit Answer</span>
              </button>

              <button
                @click="submitQuiz"
                class="btn-phantom-secondary px-6 py-2"
              >
                <span>Submit Quiz</span>
              </button>
            </div>
          </div>

          <!-- Loading State -->
          <div v-else class="text-center py-12">
            <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-phantom-blue mx-auto mb-4"></div>
            <p class="text-white/70">Loading next question...</p>
          </div>

          <!-- Answer Feedback -->
          <div v-if="showFeedback" class="mt-6 p-4 rounded-lg border">
            <div v-if="lastAnswerCorrect" class="bg-green-500/10 border-green-500/30">
              <p class="text-green-400 font-semibold">✓ Correct!</p>
            </div>
            <div v-else class="bg-red-500/10 border-red-500/30">
              <p class="text-red-400 font-semibold">✗ Incorrect</p>
              <p class="text-gray-300 text-sm mt-1">
                The correct answer was: <strong>{{ getCorrectAnswerText() }}</strong>
              </p>
            </div>
          </div>
        </div>

        <!-- Quiz Results -->
        <div v-else class="card-phantom p-8 shadow-glow-md">
          <div class="text-center">
            <h2 class="text-3xl font-bold text-white mb-6">
              {{ timeUp ? 'Time\'s Up!' : 'Quiz Completed!' }}
            </h2>
            <div v-if="timeUp" class="mb-4 p-3 bg-yellow-500/10 border border-yellow-500/30 rounded-lg">
              <p class="text-yellow-400 text-sm">⏰ The quiz time has expired. Your answers have been automatically submitted.</p>
            </div>

            <div class="bg-white/5 backdrop-blur-sm p-6 rounded-lg border border-white/10 mb-6">
              <div class="grid grid-cols-3 gap-4 text-center">
                <div>
                  <div class="text-3xl font-bold text-phantom-blue">{{ correctAnswers }}</div>
                  <div class="text-white/70">Correct Answers</div>
                </div>
                <div>
                  <div class="text-3xl font-bold text-white">{{ questionsAttempted }}</div>
                  <div class="text-white/70">Total Questions</div>
                </div>
                <div>
                  <div class="text-3xl font-bold text-phantom-indigo">{{ currentScore.toFixed(1) }}</div>
                  <div class="text-white/70">Total Score</div>
                </div>
              </div>

              <div class="mt-6">
                <div class="text-2xl font-bold text-white mb-2">
                  Percentage: {{ questionsAttempted > 0 ? Math.round((correctAnswers / questionsAttempted) * 100) : 0 }}%
                </div>
                <div class="text-lg text-white/80">
                  Performance: <span class="text-phantom-indigo font-semibold">{{ getPerformanceLevel() }}</span>
                </div>
              </div>
            </div>

            <div class="flex space-x-4 justify-center">
              <button
                @click="goBackToSession"
                class="btn-phantom-secondary px-6 py-3"
              >
                <span>Back to Session</span>
              </button>
              <button
                @click="restartQuiz"
                class="btn-phantom px-6 py-3"
              >
                <span>Take Another Quiz</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import axios from 'axios';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
import { useMessageHandler } from '@/utils/messageHandler';
import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import {
  decodeSessionCodeFromHash,
  decodeAssessmentId,
  decodeSessionId,
  isHashId,
  getDisplaySessionCode,
  extractSessionCode
} from '@/utils/hashIds';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription, AlertIcon } from '@/components/ui/alert';
import { Card, CardContent, CardHeader, CardFooter } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

const route = useRoute();
const router = useRouter();

// Message handling
const { setErrorMessage, clearMessage } = useMessageHandler();

// Reactive data
const assessmentId = ref(null);
const assessmentName = ref('');
const assessmentInfo = ref(null);
const username = ref('');
const email = ref('');
const isLoading = ref(false);
const quizStarted = ref(false);
const sessionCode = ref('');
const existingSessionCode = ref('');
const isSessionCodeValid = ref(false);
const sessionCodeCheckTimeout = ref(null);
const errorMessage = ref('');
const isRouteHashId = ref(false);

// Quiz state
const quizCompleted = ref(false);
const currentQuestion = ref(null);
const currentQuestionIndex = ref(0);
const selectedAnswer = ref('');
const answerSubmitted = ref(false);
const showFeedback = ref(false);
const lastAnswerCorrect = ref(false);
const timeUp = ref(false);
const correctAnswers = ref(0);
const questionsAttempted = ref(0);
const currentScore = ref(0); // Track the actual score from backend
const maxQuestions = ref(20); // Default, will be updated based on assessment type
const totalQuizTime = ref(3600); // Default 60 minutes in seconds (60 * 60)
const timeRemaining = ref(3600);
const timerInterval = ref(null);
const quizStartTime = ref(null);
const currentDifficulty = ref('easy');
const difficultyProgression = ['easy', 'intermediate', 'advanced'];
const allQuestions = ref([]); // Store all questions fetched from API
const questionsLoaded = ref(false);
const questionStartTime = ref(null); // Track when current question was displayed
const currentCorrectAnswer = ref(''); // Store the correct answer text for current question
const currentCorrectAnswerKey = ref(''); // Store the correct answer key for current question
const isResuming = ref(false); // Track if we're resuming an existing session
const isTimerPaused = ref(false); // Track if timer is paused due to page visibility
const lastActiveTime = ref(null); // Track when the page was last active

// Methods
const fetchAssessmentInfo = async () => {
  try {
    // Validate assessment ID before making API call
    if (!assessmentId.value || isNaN(assessmentId.value) || assessmentId.value <= 0) {
      throw new Error('Invalid assessment ID');
    }

    const response = await axios.get(`/api/admin/assessments/${assessmentId.value}`);
    const data = extractResponseData(response);

    if (data) {
      assessmentInfo.value = data;
      assessmentName.value = data.name;

      // Set max questions from assessment data
      maxQuestions.value = data.total_questions || 30; // Use total_questions from assessment

      // Set quiz duration from assessment (convert minutes to seconds)
      const durationMinutes = data.duration_minutes || 60; // Default 60 minutes
      totalQuizTime.value = durationMinutes * 60;
      timeRemaining.value = totalQuizTime.value;
    } else {
      throw new Error('Failed to extract assessment data from response');
    }
  } catch (error) {
    logError(error, 'fetchAssessmentInfo');
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || 'Failed to load assessment information');
  }
};

// Quiz functionality
const startTimer = () => {
  // Only start timer if not already started
  if (!quizStartTime.value) {
    quizStartTime.value = Date.now();
    timeUp.value = false;
    lastActiveTime.value = Date.now();

    // Try to load saved timer state first
    const hasLoadedState = loadTimerState();

    // If we're resuming and have a valid remaining time, use it
    if (isResuming.value && timeRemaining.value < totalQuizTime.value) {
      console.log(`Resuming timer with ${timeRemaining.value} seconds remaining`);
    } else if (!hasLoadedState) {
      // Otherwise start with the full time (only if we didn't load a saved state)
      timeRemaining.value = totalQuizTime.value;
    }

    // Save initial timer state
    saveTimerState();

    timerInterval.value = setInterval(() => {
      // Only decrement if timer is not paused
      if (!isTimerPaused.value) {
        timeRemaining.value--;
        lastActiveTime.value = Date.now();

        // Save timer state periodically
        if (timeRemaining.value % 10 === 0) {
          saveTimerState();
        }

        // Debug log every 30 seconds to track timer behavior
        if (timeRemaining.value % 30 === 0) {
          console.log(`Timer tick: ${timeRemaining.value} seconds remaining`);
        }

        if (timeRemaining.value <= 0) {
          clearInterval(timerInterval.value);
          timeUp.value = true;
          // End the entire quiz when time runs out
          completeQuiz();
        }
      }
    }, 1000);
  }
};

const stopTimer = () => {
  if (timerInterval.value) {
    clearInterval(timerInterval.value);
    timerInterval.value = null;
  }
};

const pauseTimer = () => {
  isTimerPaused.value = true;
  saveTimerState();
  console.log('Timer paused due to page visibility change');
};

const resumeTimer = () => {
  isTimerPaused.value = false;
  lastActiveTime.value = Date.now();
  saveTimerState();
  console.log('Timer resumed due to page visibility change');
};

const saveTimerState = () => {
  if (sessionCode.value && timeRemaining.value > 0) {
    const timerState = {
      timeRemaining: timeRemaining.value,
      lastActiveTime: lastActiveTime.value,
      isPaused: isTimerPaused.value,
      sessionCode: sessionCode.value
    };
    localStorage.setItem(`quiz_timer_${sessionCode.value}`, JSON.stringify(timerState));
  }
};

const loadTimerState = () => {
  if (sessionCode.value) {
    const savedState = localStorage.getItem(`quiz_timer_${sessionCode.value}`);
    if (savedState) {
      try {
        const timerState = JSON.parse(savedState);
        if (timerState.sessionCode === sessionCode.value) {
          timeRemaining.value = timerState.timeRemaining;
          lastActiveTime.value = timerState.lastActiveTime;
          isTimerPaused.value = timerState.isPaused || false;
          console.log('Loaded timer state:', timerState);
          return true;
        }
      } catch (error) {
        console.error('Error loading timer state:', error);
      }
    }
  }
  return false;
};

const clearTimerState = () => {
  if (sessionCode.value) {
    localStorage.removeItem(`quiz_timer_${sessionCode.value}`);
  }
};

// Page visibility handlers
const handleVisibilityChange = () => {
  if (quizStarted.value && !quizCompleted.value && timerInterval.value) {
    if (document.hidden) {
      // Page is hidden (user switched tabs, minimized browser, etc.)
      pauseTimer();
    } else {
      // Page is visible again
      resumeTimer();
    }
  }
};

const handleBeforeUnload = (event) => {
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    // Save timer state before page unload
    saveTimerState();

    // Show warning to user (optional)
    const message = 'Are you sure you want to leave? Your quiz progress will be saved but the timer will be paused.';
    event.returnValue = message;
    return message;
  }
};

const setupPageVisibilityListeners = () => {
  // Add event listeners for page visibility changes
  document.addEventListener('visibilitychange', handleVisibilityChange);
  window.addEventListener('beforeunload', handleBeforeUnload);

  // Also handle focus/blur events as backup
  window.addEventListener('blur', () => {
    if (quizStarted.value && !quizCompleted.value && timerInterval.value) {
      pauseTimer();
    }
  });

  window.addEventListener('focus', () => {
    if (quizStarted.value && !quizCompleted.value && timerInterval.value) {
      resumeTimer();
    }
  });
};

const removePageVisibilityListeners = () => {
  document.removeEventListener('visibilitychange', handleVisibilityChange);
  window.removeEventListener('beforeunload', handleBeforeUnload);
  window.removeEventListener('blur', pauseTimer);
  window.removeEventListener('focus', resumeTimer);
};

const fetchNextQuestion = async () => {
  try {
    console.log('fetchNextQuestion called with:', {
      sessionCode: sessionCode.value,
      username: username.value,
      currentDifficulty: currentDifficulty.value
    });

    // Make sure we have both session code and username
    if (!sessionCode.value) {
      console.error('Session code is missing');
      setErrorMessage('Session code is required. Please restart the quiz.');
      return;
    }

    // Make sure we have a username before proceeding
    if (!username.value) {
      console.error('Username is missing');
      setErrorMessage('Username is required. Please restart the quiz.');
      return;
    }

    // Fetch the next question
    console.log('Making API call to get next question...');
    const response = await axios.get(`/api/get_next_question/${sessionCode.value}`, {
      params: {
        user_id: username.value,
        difficulty: currentDifficulty.value
      }
    });

    console.log('API Response received:', response.data);

    // Extract data using the standardized response handler
    const data = extractResponseData(response);
    console.log('Extracted data:', data);

    // Check if response has a question
    if (!data?.question) {
      console.error('No question found in response data');
      // No more questions available, end quiz
      completeQuiz();
      return;
    }

    console.log('Setting current question:', data.question);
    // Set the current question
    currentQuestion.value = data.question;

    // Update quiz metadata
    if (data.total_questions) {
      maxQuestions.value = data.total_questions;
    }
    if (data.attempted_questions_count !== undefined) {
      questionsAttempted.value = data.attempted_questions_count;

      // If we have attempted questions, we're resuming
      if (data.attempted_questions_count > 0) {
        isResuming.value = true;
        // Set the current question index to the number of attempted questions
        // This ensures the question counter shows correctly
        currentQuestionIndex.value = data.attempted_questions_count;
        console.log(`Resuming session with ${data.attempted_questions_count} questions already attempted`);

        // When resuming, we need to get the correct answers count from the backend
        // For now, we'll fetch it when we get the session details
        // The correctAnswers will be updated when we fetch session details
      }
    }

    // Update remaining time from backend if available
    if (data.remaining_time_seconds !== undefined && data.remaining_time_seconds !== null) {
      console.log(`fetchNextQuestion - remaining_time: ${data.remaining_time_seconds}, totalQuizTime: ${totalQuizTime.value}`);

      // If backend time is reasonable, use it
      if (data.remaining_time_seconds <= totalQuizTime.value && data.remaining_time_seconds >= 0) {
        timeRemaining.value = data.remaining_time_seconds;
        console.log(`Updated remaining time from backend: ${timeRemaining.value} seconds`);
      } else if (data.remaining_time_seconds > totalQuizTime.value) {
        // If backend returns more time than expected, it might be a calculation error
        // Use a reasonable fallback but log the issue
        console.warn(`Backend returned unreasonable remaining time in fetchNextQuestion: ${data.remaining_time_seconds} seconds (exceeds total quiz time: ${totalQuizTime.value}s). Using totalQuizTime as fallback.`);
        timeRemaining.value = totalQuizTime.value;
      } else {
        console.warn(`Backend returned negative remaining time in fetchNextQuestion: ${data.remaining_time_seconds} seconds, ignoring`);
      }
    }

    // Update current score from backend if available
    if (data.current_score !== undefined && data.current_score !== null) {
      currentScore.value = data.current_score;
      console.log(`Updated current score from backend: ${currentScore.value}`);
    }

    // Reset question state
    selectedAnswer.value = '';
    answerSubmitted.value = false;
    showFeedback.value = false;
    currentCorrectAnswer.value = '';
    currentCorrectAnswerKey.value = '';

    // Record the start time for this question
    questionStartTime.value = Date.now();

    // Start timer if not already started (for both first question and resuming)
    if (!timerInterval.value) {
      startTimer();
    }

    console.log('Question setup complete');

  } catch (error) {
    console.error('Error in fetchNextQuestion:', error);
    // Handle specific error cases
    if (error.response?.status === 404) {
      // No more questions available
      completeQuiz();
    } else {
      logError(error, 'fetchNextQuestion');
      setErrorMessage(getErrorMessage(error, 'Failed to load next question'));
    }
  }
};

const initializeQuizSession = async () => {
  try {
    // Make sure we have both session code and username
    if (!sessionCode.value) {
      setErrorMessage('Session code is required. Please restart the quiz.');
      return;
    }

    // Check session status before fetching questions
    try {
      const sessionResponse = await axios.post('/api/validate_session_code', {
        session_code: sessionCode.value
      });

      const sessionData = extractResponseData(sessionResponse);
      if (sessionData) {
        const sessionStatus = sessionData.session_status;
        console.log(`Session validation - status: ${sessionStatus}, remaining_time: ${sessionData.remaining_time_seconds}, totalQuizTime: ${totalQuizTime.value}`);

        // Update remaining time from session validation if available
        if (sessionData.remaining_time_seconds !== undefined && sessionData.remaining_time_seconds !== null) {
          // For pending sessions, the backend returns the full duration which might be larger than our current totalQuizTime
          // In this case, update both totalQuizTime and timeRemaining to match the backend
          if (sessionStatus === 'pending' && sessionData.remaining_time_seconds > totalQuizTime.value) {
            // Update totalQuizTime to match the backend's duration for this assessment
            totalQuizTime.value = sessionData.remaining_time_seconds;
            timeRemaining.value = sessionData.remaining_time_seconds;
            console.log(`Session validation (pending) - updated total quiz time and remaining time: ${timeRemaining.value} seconds`);
          } else if (sessionData.remaining_time_seconds <= totalQuizTime.value && sessionData.remaining_time_seconds >= 0) {
            // For in-progress sessions or reasonable times, update normally
            timeRemaining.value = sessionData.remaining_time_seconds;
            console.log(`Session validation - remaining time: ${timeRemaining.value} seconds`);
          } else if (sessionData.remaining_time_seconds > totalQuizTime.value) {
            // For in-progress sessions with unreasonable time, this might indicate a backend issue
            // Check if this is an in-progress session that should have been calculated correctly
            if (sessionStatus === 'in_progress') {
              console.warn(`Backend returned unreasonable remaining time for in-progress session: ${sessionData.remaining_time_seconds} seconds (exceeds total quiz time: ${totalQuizTime.value}s). This suggests a backend calculation error. Using totalQuizTime as fallback.`);
              // For in-progress sessions, use the totalQuizTime as a reasonable fallback
              timeRemaining.value = totalQuizTime.value;
            } else {
              console.warn(`Backend returned unreasonable remaining time in session validation: ${sessionData.remaining_time_seconds} seconds (exceeds total quiz time: ${totalQuizTime.value}s), ignoring`);
            }
          } else {
            // Negative time - should not happen but handle gracefully
            console.warn(`Backend returned negative remaining time in session validation: ${sessionData.remaining_time_seconds} seconds, ignoring`);
          }
        }

        // Check if session is completed
        if (sessionStatus === 'completed') {
          setErrorMessage('This session has already been completed and cannot be used again.');
          completeQuiz();
          return;
        }

        // Check if session is expired
        if (sessionStatus === 'expired') {
          setErrorMessage('This session has expired and cannot be used.');
          completeQuiz();
          return;
        }

        // If session is pending, start it first
        if (sessionStatus === 'pending') {
          try {
            const startSessionResponse = await axios.post('/api/start_session', {
              session_code: sessionCode.value
            });

            const startSessionData = extractResponseData(startSessionResponse);
            if (!startSessionData) {
              throw new Error('Failed to start session');
            }

            console.log('Session started successfully');
          } catch (startError) {
            logError(startError, 'startSession');
            const errorInfo = extractErrorInfo(startError);
            setErrorMessage(errorInfo.message || 'Failed to start session. Please try again.');
            return;
          }
        } else if (sessionStatus === 'in_progress') {
          // Session is already in progress - we're resuming
          isResuming.value = true;
          console.log('Resuming in_progress session');
        } else {
          // Session is not in a valid state
          setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
          completeQuiz();
          return;
        }
      }
    } catch (error) {
      logError(error, 'checkSessionStatus');
      const errorInfo = extractErrorInfo(error);
      setErrorMessage(errorInfo.message || 'Error validating session. Please restart the quiz.');
      return;
    }

    // If we don't have a username or assessment ID, try to fetch them
    if (!username.value || !assessmentId.value) {
      try {
        const userResponse = await axios.get(`/api/admin/sessions/${sessionCode.value}/user`);
        const userData = extractResponseData(userResponse);

        if (userData && userData.username) {
          username.value = userData.username;

          // Also update assessment info if available
          if (userData.assessment_id) {
            const rawAssessmentId = userData.assessment_id;

            // Check if assessment_id is a hash
            if (isHashId(rawAssessmentId)) {
              // It's a hash, decode it
              try {
                const decodedId = await decodeAssessmentId(rawAssessmentId);
                if (decodedId) {
                  assessmentId.value = decodedId;
                } else {
                  throw new Error(`Failed to decode assessment hash: ${rawAssessmentId}`);
                }
              } catch (error) {
                logError(error, 'decodeAssessmentIdFromSession');
                setErrorMessage('Failed to load assessment information from session.');
                return;
              }
            } else {
              // It's a regular ID
              const parsedId = parseInt(rawAssessmentId);
              if (!isNaN(parsedId) && parsedId > 0) {
                assessmentId.value = parsedId;
              } else {
                logError(new Error(`Invalid assessment ID from session: ${rawAssessmentId}`), 'parseAssessmentIdFromSession');
                setErrorMessage('Invalid assessment information from session.');
                return;
              }
            }

            assessmentName.value = userData.assessment_name || '';
          }
        } else {
          setErrorMessage('Could not retrieve username for this session.');
          return;
        }
      } catch (error) {
        logError(error, 'fetchUsername');
        setErrorMessage('Error retrieving username. Please restart the quiz.');
        return;
      }
    }

    // Fetch the first question
    await fetchNextQuestion();

  } catch (error) {
    logError(error, 'initializeQuizSession');
    setErrorMessage(getErrorMessage(error, 'Failed to initialize quiz session'));
  }
};

const showNextQuestion = async () => {
  // Check if we've reached the maximum number of questions
  if (questionsAttempted.value >= maxQuestions.value) {
    completeQuiz();
    return;
  }

  // Increment the question index
  currentQuestionIndex.value++;

  // Fetch the next question from the server
  await fetchNextQuestion();
};

const selectAnswer = (answerKey) => {
  if (answerSubmitted.value || timeRemaining.value <= 0) return;

  selectedAnswer.value = answerKey;
  // Don't auto-submit, wait for manual submit button click
};

const submitCurrentAnswer = () => {
  if (!selectedAnswer.value || answerSubmitted.value) return;
  submitAnswer(selectedAnswer.value);
};

const submitQuiz = () => {
  // Confirm before submitting the entire quiz
  if (confirm('Are you sure you want to submit the quiz? This action cannot be undone.')) {
    completeQuiz();
  }
};

const submitAnswer = async (answer) => {
  if (answerSubmitted.value) return;

  answerSubmitted.value = true;
  // Don't stop the overall timer, just continue with the quiz

  // Make sure we have both session code and username
  if (!sessionCode.value) {
    setErrorMessage('Session code is required. Please restart the quiz.');
    return;
  }

  // Check session status before submitting answer
  try {
    const sessionResponse = await axios.post('/api/validate_session_code', {
      session_code: sessionCode.value
    });

    const sessionData = extractResponseData(sessionResponse);
    if (sessionData) {
      const sessionStatus = sessionData.session_status;

      // Check if session is completed
      if (sessionStatus === 'completed') {
        setErrorMessage('This session has already been completed. Cannot submit more answers.');
        completeQuiz();
        return;
      }

      // Check if session is expired
      if (sessionStatus === 'expired') {
        setErrorMessage('This session has expired. Cannot submit more answers.');
        completeQuiz();
        return;
      }

      // Check if session is in a valid state (in_progress)
      if (sessionStatus !== 'in_progress') {
        setErrorMessage(`Session is not in progress. Status: ${sessionStatus}`);
        completeQuiz();
        return;
      }
    }
  } catch (error) {
    logError(error, 'checkSessionStatusSubmit');
    if (error.response?.data?.detail) {
      setErrorMessage(error.response.data.detail);
    } else {
      setErrorMessage('Error validating session. Cannot submit answer.');
    }
    return;
  }

  if (!username.value) {
    // If username is missing but we have a session code, try to fetch the username
    try {
      const userResponse = await axios.get(`/api/admin/sessions/${sessionCode.value}/user`);
      if (userResponse.data && userResponse.data.username) {
        username.value = userResponse.data.username;
      } else {
        setErrorMessage('Could not retrieve username for this session.');
        return;
      }
    } catch (error) {
      logError(error, 'fetchUsernameSubmit');
      setErrorMessage('Error retrieving username. Please restart the quiz.');
      return;
    }
  }

  try {
    // Calculate time taken for this question
    const currentTime = Date.now();
    const timeTakenMs = questionStartTime.value ? currentTime - questionStartTime.value : 0;
    const timeTakenSeconds = Math.round(timeTakenMs / 1000); // Convert to seconds and round

    const answerData = {
      user_id: username.value,
      question_id: currentQuestion.value.que_id.toString(),
      answer: answer,
      session_code: sessionCode.value,
      time_taken: timeTakenSeconds
    };

    const response = await api.quiz.checkAndSaveAnswer(answerData);
    const data = extractResponseData(response);

    if (!data) {
      throw new Error('Failed to extract data from response');
    }

    const isCorrect = data.is_correct;
    lastAnswerCorrect.value = isCorrect;

    // Store the correct answer text and key from the API response
    if (data.correct_answer_value) {
      currentCorrectAnswer.value = data.correct_answer_value;
    } else if (data.correct_answer_key && currentQuestion.value?.options) {
      // Fallback: use the correct answer key to get the text from options
      currentCorrectAnswer.value = currentQuestion.value.options[data.correct_answer_key] || '';
    }

    // Store the correct answer key for highlighting purposes
    if (data.correct_answer_key) {
      currentCorrectAnswerKey.value = data.correct_answer_key;
    }

    // Update score and timing from backend response
    if (data.current_score !== undefined && data.current_score !== null) {
      currentScore.value = data.current_score;
      console.log(`Updated current score after answer: ${currentScore.value}`);
    }

    if (data.remaining_time_seconds !== undefined && data.remaining_time_seconds !== null) {
      console.log(`submitAnswer - remaining_time: ${data.remaining_time_seconds}, totalQuizTime: ${totalQuizTime.value}`);

      // If backend time is reasonable, use it
      if (data.remaining_time_seconds <= totalQuizTime.value && data.remaining_time_seconds >= 0) {
        timeRemaining.value = data.remaining_time_seconds;
        console.log(`Updated remaining time after answer: ${timeRemaining.value} seconds`);
      } else if (data.remaining_time_seconds > totalQuizTime.value) {
        // If backend returns more time than expected, it might be a calculation error
        console.warn(`Backend returned unreasonable remaining time after answer: ${data.remaining_time_seconds} seconds (exceeds total quiz time: ${totalQuizTime.value}s). Using totalQuizTime as fallback.`);
        timeRemaining.value = totalQuizTime.value;
      } else {
        console.warn(`Backend returned negative remaining time after answer: ${data.remaining_time_seconds} seconds, ignoring`);
      }
    }

    if (data.attempted_questions_count !== undefined) {
      questionsAttempted.value = data.attempted_questions_count;
      console.log(`Updated attempted questions count: ${questionsAttempted.value}`);
    }

    // Keep the local correct answers count for UI feedback
    if (isCorrect) {
      correctAnswers.value++;
    }

    showFeedback.value = true;

    // Progress difficulty based on performance (similar to CLI logic)
    updateDifficulty(isCorrect);

    // Show feedback for 3 seconds, then move to next question
    setTimeout(async () => {
      if (questionsAttempted.value >= maxQuestions.value) {
        completeQuiz();
      } else {
        await showNextQuestion();
      }
    }, 1500); // Reduced from 3000ms to 1000ms for faster transitions

  } catch (error) {
    logError(error, 'submitAnswer');
    setErrorMessage(getErrorMessage(error, 'Failed to submit answer'));
  }
};

const updateDifficulty = (isCorrect) => {
  // Simple difficulty progression logic similar to CLI
  const currentIndex = difficultyProgression.indexOf(currentDifficulty.value);

  if (isCorrect && currentIndex < difficultyProgression.length - 1) {
    // Move to harder difficulty if correct and not at max
    currentDifficulty.value = difficultyProgression[currentIndex + 1];
  } else if (!isCorrect && currentIndex > 0) {
    // Move to easier difficulty if incorrect and not at min
    currentDifficulty.value = difficultyProgression[currentIndex - 1];
  }
};

const completeQuiz = async () => {
  stopTimer();

  // Clear the saved timer state since quiz is completed
  clearTimerState();

  // Submit the session to mark it as completed
  if (sessionCode.value && username.value) {
    try {
      const response = await axios.post('/api/submit_session', {
        session_code: sessionCode.value,
        user_id: username.value
      });

      // Extract data using the standardized response handler
      const data = extractResponseData(response);

      // Update the final score if provided
      if (data && data.total_score !== undefined) {
        // You can use this score for display if needed
      }

    } catch (error) {
      // Don't prevent quiz completion if submission fails
      // Just log the error and continue
    }
  }

  quizCompleted.value = true;
};

const getCorrectAnswerText = () => {
  // Use the stored correct answer from the API response
  if (currentCorrectAnswer.value) {
    return currentCorrectAnswer.value;
  }

  // No fallback needed since answer field is removed from question payload
  return '';
};

const getAnswerButtonClass = (key) => {
  if (!answerSubmitted.value) {
    // Before submission - show selected answer
    if (selectedAnswer.value === key) {
      return 'bg-phantom-blue/20 border-phantom-blue';
    }
    return '';
  } else {
    // After submission - only highlight correct answer using the key from API response
    if (key === currentCorrectAnswerKey.value) {
      return 'bg-green-600/20 border-green-500';
    }
    return '';
  }
};

const getAnswerIconClass = (key) => {
  if (!answerSubmitted.value) {
    // Before submission - show selected answer
    if (selectedAnswer.value === key) {
      return 'bg-phantom-blue text-white';
    }
    return '';
  } else {
    // After submission - only highlight correct answer using the key from API response
    if (key === currentCorrectAnswerKey.value) {
      return 'bg-green-500 text-white';
    }
    return '';
  }
};

const getPerformanceLevel = () => {
  // Handle case when no questions have been attempted
  if (questionsAttempted.value === 0) return "Fail";

  const percentage = (correctAnswers.value / questionsAttempted.value) * 100;

  if (percentage === 0) return "Fail";
  if (percentage < 33) return "Basic";
  if (percentage < 62) return "Acceptable";
  if (percentage < 85) return "Exceed Expectation";
  return "OUTSTANDING";
};

const formatTime = (seconds) => {
  const hours = Math.floor(seconds / 3600);
  const minutes = Math.floor((seconds % 3600) / 60);
  const secs = seconds % 60;

  if (hours > 0) {
    return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  } else {
    return `${minutes}:${secs.toString().padStart(2, '0')}`;
  }
};

const goBackToSession = () => {
  // Navigate back to the session list
  router.push('/user-sessions');
};

const restartQuiz = () => {
  // Reset all quiz state
  quizCompleted.value = false;
  quizStarted.value = false;
  currentQuestion.value = null;
  currentQuestionIndex.value = 0;
  selectedAnswer.value = '';
  answerSubmitted.value = false;
  showFeedback.value = false;
  lastAnswerCorrect.value = false;
  timeUp.value = false;
  correctAnswers.value = 0;
  questionsAttempted.value = 0;
  currentScore.value = 0;
  currentDifficulty.value = 'easy';
  sessionCode.value = '';
  existingSessionCode.value = '';
  isSessionCodeValid.value = false;
  username.value = '';
  email.value = '';
  clearMessage();
  allQuestions.value = [];
  questionsLoaded.value = false;
  quizStartTime.value = null;
  questionStartTime.value = null;
  currentCorrectAnswer.value = '';
  currentCorrectAnswerKey.value = '';
  timeRemaining.value = totalQuizTime.value;
  isResuming.value = false;
  stopTimer();
};

const checkSessionCode = async (code) => {
  if (!code || code.length !== 6) {
    isSessionCodeValid.value = false;
    username.value = '';
    return false;
  }

  try {
    // First, check session status using the proper endpoint
    const sessionResponse = await axios.post('/api/validate_session_code', {
      session_code: code
    });

    const sessionData = extractResponseData(sessionResponse);

    if (sessionData) {
      const sessionStatus = sessionData.session_status;

      // Check if session is completed
      if (sessionStatus === 'completed') {
        setErrorMessage('This session has already been completed and cannot be used again.');
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Check if session is expired
      if (sessionStatus === 'expired') {
        setErrorMessage('This session has expired and cannot be used.');
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Check if session is in a valid state (pending or in_progress)
      if (sessionStatus !== 'pending' && sessionStatus !== 'in_progress') {
        setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }

      // Now fetch the username from the session code
      const userResponse = await axios.get(`/api/admin/sessions/${code}/user`);
      const userData = extractResponseData(userResponse);

      if (userData && userData.username) {
        // Set the username from the response
        username.value = userData.username;
        isSessionCodeValid.value = true;
        clearMessage();

        // Debug: Log the userData structure to understand what's available
        console.log('Session userData:', JSON.stringify(userData, null, 2));

        // Update assessment info - check multiple possible field names
        let rawAssessmentId = userData.assessment_id || userData.assessmentId ||
                             userData.assessment_id_hash || userData.assessmentIdHash;

        // Handle case where assessment ID is not available in user data
        if (!rawAssessmentId) {
          console.warn('No assessment ID found in user data, will try to get it from session data');
          // Try to get assessment ID from the session data itself
          const sessionAssessmentId = sessionData.assessment_id || sessionData.assessmentId ||
                                     sessionData.assessment_id_hash || sessionData.assessmentIdHash;

          if (sessionAssessmentId) {
            console.log('Found assessment ID in session data:', sessionAssessmentId);
            rawAssessmentId = sessionAssessmentId;
          } else {
            console.warn('No assessment ID found in session or user data');
            // Continue without assessment ID - it might be set via route params
          }
        }

        // Only process assessment ID if we have one
        if (rawAssessmentId) {
          // Check if assessment_id is a hash
          if (isHashId(rawAssessmentId)) {
            // It's a hash, decode it
            try {
              const decodedId = await decodeAssessmentId(rawAssessmentId);
              if (decodedId) {
                assessmentId.value = decodedId;
              } else {
                throw new Error(`Failed to decode assessment hash: ${rawAssessmentId}`);
              }
            } catch (error) {
              logError(error, 'decodeAssessmentIdInCheckSession');
              setErrorMessage('Failed to load assessment information.');
              return false;
            }
          } else {
            // It's a regular ID
            const parsedId = parseInt(rawAssessmentId);
            if (!isNaN(parsedId) && parsedId > 0) {
              assessmentId.value = parsedId;
            } else {
              logError(new Error(`Invalid assessment ID from checkSessionCode: ${rawAssessmentId}`), 'parseAssessmentIdInCheckSession');
              setErrorMessage('Invalid assessment information.');
              return false;
            }
          }
        } else {
          console.log('No assessment ID available, will continue without it');
        }

        assessmentName.value = userData.assessment_name;
        assessmentInfo.value = {
          id: assessmentId.value,
          name: userData.assessment_name,
          is_final: false // Default to false, will be updated when quiz starts
        };

        // Also set the session code
        sessionCode.value = code;

        // Fetch full assessment details to get duration and question count
        try {
          // Validate assessment ID before making API call
          if (!assessmentId.value || isNaN(assessmentId.value) || assessmentId.value <= 0) {
            throw new Error('Invalid assessment ID for fetching details');
          }

          const assessmentResponse = await axios.get(`/api/admin/assessments/${assessmentId.value}`);
          const assessmentData = extractResponseData(assessmentResponse);

          if (assessmentData) {
            const durationMinutes = assessmentData.duration_minutes || 60;
            totalQuizTime.value = durationMinutes * 60;
            timeRemaining.value = totalQuizTime.value;

            // Also update max questions from assessment
            maxQuestions.value = assessmentData.total_questions || 30;
          }
        } catch (assessmentError) {
          // Use default values if fetch fails
          logError(assessmentError, 'fetchAssessmentDetails');
        }

        return true;
      } else {
        setErrorMessage('Could not retrieve username for this session code.');
        isSessionCodeValid.value = false;
        username.value = '';
        return false;
      }
    } else {
      setErrorMessage('Invalid session code. Please check and try again.');
      isSessionCodeValid.value = false;
      username.value = '';
      return false;
    }
  } catch (error) {
    isSessionCodeValid.value = false;
    username.value = '';

    const errorInfo = extractErrorInfo(error);
    if (errorInfo.code === 404) {
      setErrorMessage('Invalid session code. Please check and try again.');
    } else {
      setErrorMessage(errorInfo.message || 'Error validating session code. Please try again.');
    }
    return false;
  }
};

const onSessionCodeChange = () => {
  // Clear previous timeout
  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  // Reset validation state
  isSessionCodeValid.value = false;
  clearMessage();

  // Only check if we have 6 digits
  if (existingSessionCode.value && existingSessionCode.value.length === 6) {
    sessionCodeCheckTimeout.value = setTimeout(() => {
      checkSessionCode(existingSessionCode.value);
    }, 500); // Debounce for 500ms
  } else {
    username.value = '';
  }
};

const startQuiz = async () => {

  // If using existing session code, verify it's valid
  if (existingSessionCode.value) {
    if (!isSessionCodeValid.value) {
      // Try to validate the session code one more time
      const isValid = await checkSessionCode(existingSessionCode.value);
      if (!isValid) {
        setErrorMessage('Please enter a valid session code or username');
        return;
      }
    }

    // Double-check session status before starting quiz
    try {
      const sessionResponse = await axios.post('/api/validate_session_code', {
        session_code: existingSessionCode.value
      });

      const sessionData = extractResponseData(sessionResponse);

      if (sessionData) {
        const sessionStatus = sessionData.session_status;

        // Update remaining time from session validation if available
        if (sessionData.remaining_time_seconds !== undefined && sessionData.remaining_time_seconds !== null) {
          console.log(`Session validation in startQuiz - status: ${sessionStatus}, remaining_time: ${sessionData.remaining_time_seconds}, totalQuizTime: ${totalQuizTime.value}`);

          // For pending sessions, the backend returns the full duration which might be larger than our current totalQuizTime
          if (sessionStatus === 'pending' && sessionData.remaining_time_seconds > totalQuizTime.value) {
            // Update totalQuizTime to match the backend's duration for this assessment
            totalQuizTime.value = sessionData.remaining_time_seconds;
            timeRemaining.value = sessionData.remaining_time_seconds;
            console.log(`Session validation in startQuiz (pending) - updated total quiz time and remaining time: ${timeRemaining.value} seconds`);
          } else if (sessionData.remaining_time_seconds <= totalQuizTime.value && sessionData.remaining_time_seconds >= 0) {
            // For in-progress sessions or reasonable times, update normally
            timeRemaining.value = sessionData.remaining_time_seconds;
            console.log(`Session validation in startQuiz - remaining time: ${timeRemaining.value} seconds`);
          } else if (sessionData.remaining_time_seconds > totalQuizTime.value) {
            // For in-progress sessions with unreasonable time, this might indicate a backend issue
            if (sessionStatus === 'in_progress') {
              console.warn(`Backend returned unreasonable remaining time for in-progress session in startQuiz: ${sessionData.remaining_time_seconds} seconds (exceeds total quiz time: ${totalQuizTime.value}s). Using totalQuizTime as fallback.`);
              timeRemaining.value = totalQuizTime.value;
            } else {
              console.warn(`Backend returned unreasonable remaining time in startQuiz: ${sessionData.remaining_time_seconds} seconds, ignoring`);
            }
          } else {
            console.warn(`Backend returned negative remaining time in startQuiz: ${sessionData.remaining_time_seconds} seconds, ignoring`);
          }
        }

        // Check if session is completed
        if (sessionStatus === 'completed') {
          setErrorMessage('This session has already been completed and cannot be used again.');
          return;
        }

        // Check if session is expired
        if (sessionStatus === 'expired') {
          setErrorMessage('This session has expired and cannot be used.');
          return;
        }

        // If session is pending, start it first
        if (sessionStatus === 'pending') {
          try {
            const startSessionResponse = await axios.post('/api/start_session', {
              session_code: existingSessionCode.value
            });

            const startSessionData = extractResponseData(startSessionResponse);
            if (!startSessionData) {
              throw new Error('Failed to start session');
            }

            console.log('Session started successfully');
          } catch (startError) {
            logError(startError, 'startSession');
            const errorInfo = extractErrorInfo(startError);
            setErrorMessage(errorInfo.message || 'Failed to start session. Please try again.');
            return;
          }
        } else if (sessionStatus === 'in_progress') {
          // Session is already in progress - we're resuming
          isResuming.value = true;
          console.log('Resuming in_progress session in startQuiz');
        } else {
          // Session is not in a valid state
          setErrorMessage(`Session is not available. Status: ${sessionStatus}`);
          return;
        }
      }
    } catch (error) {
      if (error.response?.data?.detail) {
        setErrorMessage(error.response.data.detail);
      } else {
        setErrorMessage('Error validating session. Please try again.');
      }
      return;
    }

    // Session code is valid, proceed with the quiz
    sessionCode.value = existingSessionCode.value; // Set the session code
    quizStarted.value = true;
    sessionCode.value = existingSessionCode.value;

    // Initialize the quiz session and fetch first question
    setTimeout(() => {
      initializeQuizSession();
    }, 1000);
    return;

  }

  // Otherwise, create a new session with the provided username
  if (!username.value.trim()) {
    setErrorMessage('Please enter your username');
    return;
  }

  isLoading.value = true;
  clearMessage();

  try {
    // Ensure we have a valid assessment ID
    let assessmentIdToUse = assessmentId.value;

    // Make sure we have a numeric ID for the API (should already be decoded)
    if (assessmentIdToUse && typeof assessmentIdToUse !== 'number') {
      assessmentIdToUse = parseInt(assessmentIdToUse);
    }

    if (!assessmentIdToUse || isNaN(assessmentIdToUse)) {
      setErrorMessage('Invalid assessment ID. Please try again.');
      return;
    }

    const response = await axios.post('/api/admin/sessions', {
      assessment_id: assessmentIdToUse,
      usernames: username.value.trim()
    });

    const data = extractResponseData(response);

    if (data && data.sessions && data.sessions.length > 0) {
      // Extract session code from the first session
      const sessionData = data.sessions[0];

      // Use the standardized session code extraction
      const extractedCode = extractSessionCode(sessionData);

      if (extractedCode) {
        sessionCode.value = extractedCode;
      } else if (sessionData.id_hash) {
        // Try to decode the hash to get the session code
        try {
          const decodedCode = await decodeSessionCodeFromHash(sessionData.id_hash);
          if (decodedCode) {
            sessionCode.value = decodedCode;
          } else {
            throw new Error('Failed to decode session hash');
          }
        } catch (error) {
          logError(error, 'decodeSessionHashForNewSession');
          setErrorMessage('Failed to create session code. Please try again.');
          return;
        }
      } else {
        setErrorMessage('Invalid session data received. Please try again.');
        return;
      }

      quizStarted.value = true;

      // Initialize the quiz session and fetch first question
      setTimeout(() => {
        initializeQuizSession();
      }, 1000);
    } else {
      setErrorMessage('Failed to create quiz session. Please try again.');
    }
  } catch (error) {
    const errorInfo = extractErrorInfo(error);
    setErrorMessage(errorInfo.message || 'Failed to start quiz. Please try again.');
  } finally {
    isLoading.value = false;
  }
};



// Process route params from URL
const processRouteParams = async () => {
  // Check if we have a session code in the route
  if (route.params.sessionCode) {
    const routeSessionCode = route.params.sessionCode;

    // Check if it's a hash ID (contains letters)
    isRouteHashId.value = isHashId(routeSessionCode);

    if (isRouteHashId.value) {
      try {
        // Decode the hash to get the session code
        const decodedCode = await decodeSessionCodeFromHash(routeSessionCode);
        if (decodedCode) {
          // Set the decoded session code
          existingSessionCode.value = decodedCode;
          sessionCode.value = decodedCode;

          // Validate the session code and get assessment ID
          const isValid = await checkSessionCode(decodedCode);

          // If validation failed, clear the session code
          if (!isValid) {
            sessionCode.value = '';
            existingSessionCode.value = '';
          }
        } else {
          setErrorMessage('Invalid session code format.');
        }
      } catch (error) {
        logError(error, 'decodeSessionHash');
        setErrorMessage('Failed to decode session code.');
      }
    } else {
      // It's a regular session code
      existingSessionCode.value = routeSessionCode;

      // Validate the session code and get assessment ID
      const isValid = await checkSessionCode(routeSessionCode);

      // If validation failed, clear the session code
      if (!isValid) {
        sessionCode.value = '';
        existingSessionCode.value = '';
      }
    }
  }

  // Check if we have an assessment ID in the route (for direct assessment access)
  if (route.params.assessmentId) {
    const routeAssessmentId = route.params.assessmentId;

    // Check if it's a hash ID
    if (isHashId(routeAssessmentId)) {
      try {
        // Decode the assessment hash to get the real assessment ID
        const decodedId = await decodeAssessmentId(routeAssessmentId);
        if (decodedId) {
          assessmentId.value = decodedId;
        } else {
          setErrorMessage('Invalid assessment link.');
          return;
        }
      } catch (error) {
        logError(error, 'decodeAssessmentHash');
        setErrorMessage('Failed to decode assessment link.');
        return;
      }
    } else {
      // It's a regular assessment ID (fallback for development)
      const parsedId = parseInt(routeAssessmentId);
      if (!isNaN(parsedId) && parsedId > 0) {
        assessmentId.value = parsedId;
      } else {
        setErrorMessage('Invalid assessment ID in URL.');
        return;
      }
    }
  }
};

// Lifecycle
onMounted(async () => {
  // Disable scrolling directly
  document.body.style.overflow = 'hidden';
  document.body.style.height = '100vh';
  document.body.style.position = 'fixed';
  document.body.style.width = '100%';
  document.documentElement.style.overflow = 'hidden';

  // Set up page visibility listeners
  setupPageVisibilityListeners();

  try {
    // Process route params to get session code
    await processRouteParams();

    if (assessmentId.value) {
      // If we have an assessment ID, fetch its info
      await fetchAssessmentInfo();
    }

    if (sessionCode.value) {
      // If we have a valid session code, start the quiz
      quizStarted.value = true;
      setTimeout(() => {
        initializeQuizSession();
      }, 1000);
    } else if (!assessmentId.value) {
      // If we have neither a session code nor an assessment ID, show an error
      setErrorMessage('Invalid assessment link or session code.');
    }
  } catch (error) {
    logError(error, 'onMounted');
    setErrorMessage('Failed to initialize quiz. Please try again.');
  }
});

onUnmounted(() => {
  // Clean up timer when component is destroyed
  stopTimer();

  // Save timer state before unmounting (in case user navigates away)
  if (quizStarted.value && !quizCompleted.value && timeRemaining.value > 0) {
    saveTimerState();
  }

  // Remove page visibility listeners
  removePageVisibilityListeners();

  if (sessionCodeCheckTimeout.value) {
    clearTimeout(sessionCodeCheckTimeout.value);
  }

  // Restore body styles
  document.body.style.overflow = '';
  document.body.style.height = '';
  document.body.style.position = '';
  document.body.style.width = '';
  document.documentElement.style.overflow = '';
});
</script>

<style scoped>
/* Phantom theme styles are imported from phantom-theme.css */
</style>
