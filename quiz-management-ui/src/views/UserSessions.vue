<template>
  <PhantomLayout
    title="My Sessions"
  >
    <div class="p-6">
      <!-- Loading indicator -->
      <div v-if="isLoading" class="flex justify-center items-center py-12">
        <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"></div>
        <span class="ml-3 text-white/80">Loading sessions...</span>
      </div>

      <!-- Error message -->
      <div v-if="message && !isSuccess" class="bg-red-500/10 backdrop-blur-sm border border-red-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-red-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
        </svg>
        {{ message }}
      </div>

      <!-- Success message -->
      <div v-if="message && isSuccess" class="bg-green-500/10 backdrop-blur-sm border border-green-500/30 text-white px-6 py-4 rounded-xl mb-6 flex items-center">
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-3 text-green-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7" />
        </svg>
        {{ message }}
      </div>

      <!-- Search bar -->
      <div class="flex flex-col md:flex-row justify-between items-start md:items-center gap-4 mb-6">
        <div class="w-full md:w-auto flex-1">
          <div class="relative">
            <input
              type="text"
              v-model="searchQuery"
              placeholder="Search sessions..."
              class="w-full bg-white/5 border border-white/10 rounded-xl px-4 py-2.5 pl-10 text-white focus:outline-none focus:ring-2 focus:ring-phantom-blue/50"
            />
            <div class="absolute left-3 top-1/2 transform -translate-y-1/2 text-white/50">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- No sessions message -->
      <div v-if="!isLoading && pendingSessions.length === 0 && inProgressSessions.length === 0 && completedSessions.length === 0" class="text-center py-16">
        <div class="w-20 h-20 mx-auto mb-6 rounded-full bg-white/5 flex items-center justify-center">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-white/40" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
        </div>
        <h3 class="text-xl font-medium text-white mb-2">No sessions found</h3>
        <p class="text-white/60 mb-6">You don't have any assessment sessions yet</p>
      </div>

      <!-- Pending Sessions Section -->
      <div v-if="pendingSessions.length > 0" class="mb-8">
        <h3 class="text-xl font-medium text-white mb-4">Pending Sessions</h3>

        <div class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Session Code</th>
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Created</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(session, index) in filteredPendingSessions" :key="index"
                  :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150">
                <td class="py-4 px-6">
                  <span class="font-mono bg-white/5 text-phantom-blue px-3 py-1 rounded-full border border-phantom-blue/20">
                    {{ getDisplaySessionCode(session) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white">{{ session.assessment_name }}</td>
                <td class="py-4 px-6 text-white/80">{{ formatDate(session.created_at) }}</td>
                <td class="py-4 px-6">
                  <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-yellow-500/10 text-yellow-400 border border-yellow-500/20">
                    Pending
                  </span>
                </td>
                <td class="py-4 px-6">
                  <button
                    @click="startSession(session)"
                    class="btn-phantom-secondary text-xs px-3 py-1.5"
                  >
                    Start
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- In Progress Sessions Section -->
      <div v-if="inProgressSessions.length > 0" class="mb-8">
        <h3 class="text-xl font-medium text-white mb-4">In Progress Sessions</h3>

        <div class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Session Code</th>
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Started</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Action</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(session, index) in filteredInProgressSessions" :key="`in-progress-${index}`"
                  :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150">
                <td class="py-4 px-6">
                  <span class="font-mono bg-white/5 text-phantom-blue px-3 py-1 rounded-full border border-phantom-blue/20">
                    {{ getDisplaySessionCode(session) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white">{{ session.assessment_name }}</td>
                <td class="py-4 px-6 text-white/80">{{ formatDate(session.started_at || session.updated_at) }}</td>
                <td class="py-4 px-6">
                  <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-blue-500/10 text-blue-400 border border-blue-500/20">
                    In Progress
                  </span>
                </td>
                <td class="py-4 px-6">
                  <button
                    @click="startSession(session)"
                    class="btn-phantom-secondary text-xs px-3 py-1.5"
                  >
                    Continue
                  </button>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>

      <!-- Completed Sessions Section -->
      <div v-if="completedSessions.length > 0">
        <h3 class="text-xl font-medium text-white mb-4">Completed Sessions</h3>

        <div class="overflow-x-auto rounded-xl border border-white/10 backdrop-blur-sm">
          <table class="w-full text-left">
            <thead>
              <tr class="border-b border-white/10 bg-white/5">
                <th class="py-4 px-6 text-white/80 font-medium">Session Code</th>
                <th class="py-4 px-6 text-white/80 font-medium">Assessment</th>
                <th class="py-4 px-6 text-white/80 font-medium">Completed</th>
                <th class="py-4 px-6 text-white/80 font-medium">Status</th>
                <th class="py-4 px-6 text-white/80 font-medium">Score</th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(session, index) in filteredCompletedSessions" :key="`completed-${index}`"
                  :class="index % 2 === 0 ? 'bg-white/[0.03]' : 'bg-transparent'"
                  class="border-b border-white/5 hover:bg-white/[0.05] transition-colors duration-150 cursor-pointer"
                  @click="viewSessionDetails(session)">
                <td class="py-4 px-6">
                  <span class="font-mono bg-white/5 text-phantom-blue px-3 py-1 rounded-full border border-phantom-blue/20">
                    {{ getDisplaySessionCode(session) }}
                  </span>
                </td>
                <td class="py-4 px-6 text-white">{{ session.assessment_name }}</td>
                <td class="py-4 px-6 text-white/80">{{ formatDate(session.completed_at) }}</td>
                <td class="py-4 px-6">
                  <span class="inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium bg-green-500/10 text-green-400 border border-green-500/20">
                    Completed
                  </span>
                </td>
                <td class="py-4 px-6">
                  <span class="font-medium text-white">
                    {{ session.score || 'N/A' }}
                  </span>
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </PhantomLayout>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { api } from '@/services/api';
import { getErrorMessage, logError } from '@/utils/errorHandling';
  import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
import { useMessageHandler } from '@/utils/messageHandler';
import { decodeSessionHash } from '@/utils/hashidUtils';
import PhantomLayout from '@/components/layout/Layout.vue';

const router = useRouter();

// Message handling
const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

// Shared state
const isLoading = ref(false);
const searchQuery = ref('');

// Sessions data
const allSessions = ref([]);
const pendingSessions = ref([]);

// Filtered pending sessions based on search query
const filteredPendingSessions = computed(() => {
  if (!searchQuery.value) return pendingSessions.value;

  const query = searchQuery.value.toLowerCase();
  return pendingSessions.value.filter(session => {
    const displayCode = getDisplaySessionCode(session);
    return (
      (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
      (session.session_code && session.session_code.toLowerCase().includes(query)) ||
      (displayCode && displayCode.toLowerCase().includes(query)) ||
      (session.assessment_name && session.assessment_name.toLowerCase().includes(query))
    );
  });
});

// Computed property for in-progress sessions
const inProgressSessions = computed(() => {
  return allSessions.value.filter(session =>
    session.status === 'in_progress' || session.status === 'started'
  );
});

// Filtered in-progress sessions based on search query
const filteredInProgressSessions = computed(() => {
  if (!searchQuery.value) return inProgressSessions.value;

  const query = searchQuery.value.toLowerCase();
  return inProgressSessions.value.filter(session => {
    const displayCode = getDisplaySessionCode(session);
    return (
      (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
      (session.session_code && session.session_code.toLowerCase().includes(query)) ||
      (displayCode && displayCode.toLowerCase().includes(query)) ||
      (session.assessment_name && session.assessment_name.toLowerCase().includes(query))
    );
  });
});

// Computed property for completed sessions
const completedSessions = computed(() => {
  return allSessions.value.filter(session =>
    session.status === 'completed' || session.status === 'finished'
  );
});

// Filtered completed sessions based on search query
const filteredCompletedSessions = computed(() => {
  if (!searchQuery.value) return completedSessions.value;

  const query = searchQuery.value.toLowerCase();
  return completedSessions.value.filter(session => {
    const displayCode = getDisplaySessionCode(session);
    return (
      (session.id_hash && session.id_hash.toLowerCase().includes(query)) ||
      (session.session_code && session.session_code.toLowerCase().includes(query)) ||
      (displayCode && displayCode.toLowerCase().includes(query)) ||
      (session.assessment_name && session.assessment_name.toLowerCase().includes(query))
    );
  });
});

// Format date for display
const formatDate = (dateString) => {
  if (!dateString) return 'N/A';
  const date = new Date(dateString);
  return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
};

// Get display-friendly session code (decoded from hash)
const getDisplaySessionCode = (session) => {
  // If we have a hash, decode it for display
  if (session.id_hash) {
    const decoded = decodeSessionHash(session.id_hash);
    if (decoded) {
      return decoded;
    }
  }
  // Fallback to raw session code if available
  return session.session_code || 'N/A';
};

// Navigate to session details page
const viewSessionDetails = (session) => {
  // Get the session code - prefer hashed id_hash for security
  const sessionCode = session.id_hash || session.session_code || 'unknown';

  // For now, we'll just show a message
  setSuccessMessage(`Viewing details for session ${sessionCode} is not implemented yet.`);
  // In the future, you could implement a detailed view:
  // router.push(`/user/session/${sessionCode}`);
};

// Start a session
const startSession = (session) => {
  // Check if the session is in progress
  const isInProgress = session.status === 'in_progress' || session.status === 'started';

  // Get the session code - prefer hashed id_hash for security
  const sessionCode = session.id_hash || session.session_code;

  if (!sessionCode) {
    setErrorMessage('Invalid session code');
    return;
  }

  // Redirect to the quiz page with the session code and resume parameter if in progress
  if (isInProgress) {
    router.push({
      path: `/quiz/${sessionCode}`,
      query: { resume: 'true' }
    });
  } else {
    router.push(`/quiz/${sessionCode}`);
  }
};

// Get user email from localStorage
const getUserEmail = () => {
  try {
    const userInfoStr = localStorage.getItem('user_info');
    if (!userInfoStr) {
      console.warn('No user info found in localStorage');
      return '<EMAIL>'; // Default email for testing
    }

    const userInfo = JSON.parse(userInfoStr);
    const email = userInfo.email;

    if (!email) {
      console.warn('No email found in user info');
      return '<EMAIL>'; // Default email for testing
    }

    return email;
  } catch (error) {
    console.error('Error getting user email:', error);
    return '<EMAIL>'; // Default email for testing
  }
};

// Fetch user sessions from API
const fetchUserSessions = async () => {
  try {
    isLoading.value = true;
    clearMessage();

    const userEmail = getUserEmail();
    console.log('Fetching sessions for user:', userEmail);

    const response = await api.user.getUserSessions(userEmail);
    console.log('User sessions response:', response);

    // Extract data using the standardized response handler
    const responseData = extractResponseData(response);
    console.log('Extracted response data:', responseData);

    // Check if we have sessions in the response
    if (responseData && responseData.sessions) {
      // Process sessions to ensure we have session codes
      const sessions = responseData.sessions || [];

      // For each session, make sure we have a session_code
      sessions.forEach(session => {
        // If we don't have a session_code but have an id_hash, try to decode it
        if (!session.session_code && session.id_hash) {
          const decodedCode = decodeSessionHash(session.id_hash);
          if (decodedCode) {
            session.session_code = decodedCode;
            console.log(`Decoded session code ${decodedCode} from hash ${session.id_hash}`);
          }
        }
      });

      allSessions.value = sessions;
      console.log('All sessions:', allSessions.value);

      // Log the first session to see its structure
      if (allSessions.value.length > 0) {
        console.log('First session structure:', JSON.stringify(allSessions.value[0], null, 2));
      }
    } else {
      console.error('Unexpected response format:', response);
      setErrorMessage('Received an unexpected response format from the server');
      allSessions.value = [];
    }

    // Filter for pending sessions only
    pendingSessions.value = allSessions.value.filter(session =>
      (session.status === 'pending' || session.status === 'created' || !session.status) &&
      session.status !== 'in_progress' && session.status !== 'started'
    );
    console.log('Pending sessions:', pendingSessions.value);

    // If we got a successful response but no sessions, show a message
    if (allSessions.value.length === 0) {
      setSuccessMessage('No sessions found. You have not been assigned any assessments yet.');
    }
  } catch (error) {
    console.error('Error fetching sessions:', error);
    logError(error, 'fetchUserSessions');
    setErrorMessage(getErrorMessage(error, 'Failed to fetch sessions'));
  } finally {
    isLoading.value = false;
  }
};

onMounted(() => {
  fetchUserSessions();
});
</script>
