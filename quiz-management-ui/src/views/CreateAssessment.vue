<template>
    <PhantomLayout
      title="Create Assessment"
    >
      <!-- Full-width container -->
      <div class="w-full p-7 -mt-14">
        <!-- Back to Assessment List Button - Top Right -->
        <div class="flex justify-end mb-8">
          <button
            @click="navigateTo('/list-assessments')"
            class="btn-phantom-secondary px-6 py-3 text-base"
          >
            <span class="flex items-center">
              <SvgIcon name="arrow-left" class="mr-2" />
              Back to Assessment List
            </span>
          </button>
        </div>

        <form @submit.prevent="createAssessment" class="space-y-12">
          <!-- Basic Information and Skills Selection Side by Side -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <!-- Basic Information Section -->
            <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Basic Information</h2>

              <div class="space-y-6">
                <!-- Assessment Name -->
                <div>
                  <label for="assessmentName" class="block text-white font-medium mb-2">Assessment Name</label>
                  <input
                    id="assessmentName"
                    name="assessmentName"
                    v-model="assessmentName"
                    type="text"
                    autocomplete="off"
                    placeholder="e.g. DevOps Basics"
                    required
                    class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
                  />
                </div>

                <!-- Assessment Duration -->
                <div>
                  <label for="assessmentDuration" class="block text-white font-medium mb-2">Assessment Duration (minutes)</label>
                  <div class="flex items-center">
                    <input
                      type="number"
                      id="assessmentDuration"
                      name="assessmentDuration"
                      v-model="assessmentDuration"
                      min="5"
                      max="180"
                      autocomplete="off"
                      class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50"
                      required
                    />
                  </div>
                  <div class="mt-1 text-xs text-white/60">
                    Set the time limit for completing this assessment
                  </div>
                </div>

                <!-- Assessment Description -->
                <div>
                  <label for="assessmentDescription" class="block text-white font-medium mb-2">Assessment Description</label>
                  <textarea
                    id="assessmentDescription"
                    name="assessmentDescription"
                    v-model="assessmentDescription"
                    autocomplete="off"
                    placeholder="e.g. A comprehensive assessment of DevOps fundamentals including CI/CD pipelines, containerization, and infrastructure automation"
                    required
                    class="w-full bg-white/5 border border-white/10 rounded-lg px-4 py-2.5 text-white placeholder-white/40 focus:outline-none focus:ring-2 focus:ring-phantom-blue/50 focus:border-phantom-blue/50 resize-y min-h-[100px]"
                  ></textarea>
                  <div class="mt-1 text-xs text-white/60">
                    Provide a detailed description of what this assessment covers (minimum 20 characters)
                  </div>
                </div>
              </div>
            </section>

            <!-- Skills Section with Question Selection Mode Below -->
            <div class="space-y-8">
              <!-- Skills Section -->
              <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8" style="position: relative; z-index: 1000;">
                <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Skills Selection</h2>

                <div>
                  <label class="block text-white font-medium mb-2">Select Skills</label>
                  <div class="mb-2 text-xs text-white/60">You can select multiple skills for this assessment</div>

                  <div class="flex items-center space-x-2">
                    <Select
                      v-model="selectedSkillIds"
                      :disabled="isLoading"
                      multiple
                      class="w-full"
                      @update:modelValue="onSkillSelectionChange"
                    >
                      <SelectTrigger class="w-full bg-white/5 border border-white/10 text-white focus:ring-phantom-blue/50 focus:border-phantom-blue/50 hover:bg-white/10">
                        <SelectValue placeholder="Select skills..." />
                      </SelectTrigger>
                      <SelectContent class="bg-gray-900/95 backdrop-blur-sm border border-white/10 text-white">
                        <div class="py-1.5 px-3 text-sm text-white/60" v-if="skills.length > 0">
                          {{ skills.length }} skills available
                        </div>


                        <!-- No skills message -->
                        <div v-if="skills.length === 0" class="py-4 px-3 text-sm text-white/60 text-center">
                          No skills available. Please check the console for errors.
                        </div>

                        <!-- Skills list -->
                        <template v-else>
                          <SelectItem v-for="skill in skills" :key="skill.id" :value="skill.id">
                            {{ skill.name || 'Unnamed Skill' }}
                          </SelectItem>
                        </template>
                      </SelectContent>
                    </Select>
                    <Button
                      @click="clearSkillSelection"
                      variant="outline"
                      size="sm"
                      :disabled="isLoading || selectedSkillIds.length === 0"
                      class="whitespace-nowrap border-phantom-blue/30 text-white hover:bg-phantom-blue/20"
                    >
                      Clear
                    </Button>
                  </div>

                  <!-- Selected Skills Display -->
                  <div v-if="validSelectedSkillIds.length > 0" class="mt-4 flex flex-wrap gap-2">
                    <div
                      v-for="skillId in validSelectedSkillIds"
                      :key="skillId"
                      class="inline-flex items-center bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30 text-xs px-3 py-1.5 rounded-full"
                    >
                      {{ getSkillName(skillId) }}
                      <button
                        @click="removeSkill(skillId)"
                        type="button"
                        class="ml-2 text-phantom-blue hover:text-white hover:bg-phantom-blue/30 rounded-full p-0.5 transition-colors"
                      >
                        <SvgIcon name="x" size="xs" />
                      </button>
                    </div>
                  </div>
                  <div v-if="hasInteractedWithSkills && selectedSkillIds.length === 0" class="mt-2 text-xs text-red-400">
                    Please select at least one skill
                  </div>
                </div>
              </section>

              <!-- Question Selection Mode Section -->
              <section class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8 question-selection-mode" style="position: relative; z-index: 1;">
                <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Question Selection Mode</h2>

                <div>
                  <label class="block text-white font-medium mb-3">How should questions be selected?</label>
                  <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group" @click="questionSelectionMode = 'dynamic'">
                      <div class="flex items-center">
                        <div class="relative flex items-center justify-center">
                          <input
                            type="radio"
                            id="dynamicMode"
                            name="questionSelectionMode"
                            value="dynamic"
                            v-model="questionSelectionMode"
                            class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                          />
                          <div v-if="questionSelectionMode === 'dynamic'" class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"></div>
                        </div>
                        <label for="dynamicMode" class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors">
                          Dynamic Mode
                        </label>
                      </div>
                      <div class="mt-3 ml-8 text-sm text-white/80">
                        Questions are randomly selected from the skill's question pool for each session.
                      </div>
                    </div>

                    <div class="bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-5 hover:bg-white/10 transition-colors cursor-pointer group" @click="questionSelectionMode = 'fixed'">
                      <div class="flex items-center">
                        <div class="relative flex items-center justify-center">
                          <input
                            type="radio"
                            id="fixedMode"
                            name="questionSelectionMode"
                            value="fixed"
                            v-model="questionSelectionMode"
                            class="h-5 w-5 text-phantom-blue focus:ring-phantom-blue/50 border-white/20 bg-white/5"
                          />
                          <div v-if="questionSelectionMode === 'fixed'" class="absolute inset-0 bg-phantom-blue/20 rounded-full animate-ping-slow"></div>
                        </div>
                        <label for="fixedMode" class="ml-3 text-base text-white font-medium group-hover:text-phantom-blue transition-colors">
                          Fixed Mode
                        </label>
                      </div>
                      <div class="mt-3 ml-8 text-sm text-white/80">
                        Same questions for all sessions. You'll select specific questions below.
                      </div>
                    </div>
                  </div>

                  <div class="mt-6 text-sm text-white/90 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5">
                    <p class="font-medium text-phantom-indigo">Important Note:</p>
                    <ul class="list-disc list-inside mt-2 space-y-1 ml-2">
                      <li><strong>Dynamic mode:</strong> Questions are randomly selected from the skill's question pool for each session.</li>
                      <li><strong>Fixed mode:</strong> After creating the assessment, you'll need to go to "Add Fixed Questions" to select specific questions that will be used for all sessions.</li>
                    </ul>
                  </div>
                </div>
              </section>
            </div>
          </div>
          <!-- Status Section -->
          <section class="w-full">
            <!-- Loading indicator -->
            <div v-if="isLoading" class="flex justify-center items-center py-8 bg-white/5 backdrop-blur-sm rounded-xl">
              <div class="animate-spin rounded-full h-10 w-10 border-t-2 border-b-2 border-phantom-blue"></div>
              <span class="ml-4 text-white text-lg">Creating assessment...</span>
            </div>

            <!-- Error/Success message -->
            <div v-if="message" class="my-4">
              <div
                :class="isSuccess ? 'bg-green-500/10 border-green-500/30' : 'bg-red-500/10 border-red-500/30'"
                class="px-6 py-4 rounded-xl border backdrop-blur-sm text-white"
              >
                {{ message }}
              </div>
            </div>

            <!-- Assessment details after creation -->
            <div v-if="createdAssessmentDetails" class="mt-8 bg-white/5 backdrop-blur-sm border border-white/10 rounded-xl p-8">
              <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Assessment Created Successfully</h2>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4 text-white/80">
                <div>
                  <h3 class="text-white/60 text-sm mb-1">Assessment Name</h3>
                  <p class="text-white">{{ createdAssessmentDetails.assessment_base_name }}</p>
                </div>

                <div>
                  <h3 class="text-white/60 text-sm mb-1">Assessment ID</h3>
                  <p class="text-white">{{ createdAssessmentDetails.assessment_id }}</p>
                </div>

                <div>
                  <h3 class="text-white/60 text-sm mb-1">Duration</h3>
                  <p class="text-white">{{ createdAssessmentDetails.duration || assessmentDuration }} minutes</p>
                </div>

                <div>
                  <h3 class="text-white/60 text-sm mb-1">Question Selection Mode</h3>
                  <p class="text-white">
                    <span class="capitalize">{{ createdAssessmentDetails.question_selection_mode }}</span>
                    <span v-if="createdAssessmentDetails.question_selection_mode === 'fixed'" class="ml-2 text-xs text-phantom-indigo">
                      (Add fixed questions below)
                    </span>
                  </p>
                </div>

                <div class="col-span-2">
                  <h3 class="text-white/60 text-sm mb-1">Assessment Description</h3>
                  <p class="text-white">{{ createdAssessmentDetails.assessment_description || assessmentDescription }}</p>
                </div>

                <div class="col-span-2" v-if="createdAssessmentDetails.skill_ids && createdAssessmentDetails.skill_ids.length > 0">
                  <h3 class="text-white/60 text-sm mb-2">Skills</h3>
                  <div class="flex flex-wrap gap-2">
                    <span
                      v-for="skillId in createdAssessmentDetails.skill_ids.filter(id => id !== null && id !== undefined)"
                      :key="skillId"
                      class="inline-block bg-phantom-blue/20 text-phantom-blue border border-phantom-blue/30 text-xs px-3 py-1.5 rounded-full"
                    >
                      {{ getSkillName(skillId) }}
                    </span>
                  </div>
                </div>

                <div v-if="createdAssessmentDetails.total_questions_available" class="col-span-2">
                  <h3 class="text-white/60 text-sm mb-1">Available Questions</h3>
                  <p class="text-white">{{ createdAssessmentDetails.total_questions_available }}</p>
                </div>
              </div>

              <div class="mt-8 pt-6 border-t border-white/10">
                <h3 class="text-lg font-medium text-white mb-3 bg-gradient-to-r from-phantom-blue to-phantom-indigo bg-clip-text text-transparent">Next Steps</h3>
                <p class="text-white/80 mb-4">
                  Use the "Generate Sessions" menu to create session codes for this assessment.
                </p>
                <button
                  @click="navigateTo('/generate-sessions')"
                  class="btn-phantom px-6 py-3"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1 1 21 9z" />
                    </svg>
                    Go to Generate Sessions
                  </span>
                </button>
              </div>
            </div>
          </section>

          <!-- Add Fixed Questions Section (shown when fixed mode is selected) -->
          <section v-if="questionSelectionMode === 'fixed'" class="card-phantom p-6 mt-8">
            <h2 class="text-xl font-semibold text-white mb-6 bg-gradient-to-r from-phantom-indigo to-phantom-purple bg-clip-text text-transparent">Add Fixed Questions</h2>

            <!-- Instructions -->
            <div class="mb-6 bg-phantom-indigo/10 backdrop-blur-sm border border-phantom-indigo/20 rounded-xl p-5">
              <p class="text-white/90 flex items-start">
                <SvgIcon name="warning" class="mr-2 flex-shrink-0 mt-0.5 text-phantom-indigo" />
                <span>
                  Select your questions below. When you click "Create Assessment", both the assessment and the selected questions will be created together.
                </span>
              </p>
            </div>

            <!-- Question Distribution -->
            <div class="mb-8">
              <h3 class="text-lg font-medium text-gray-300 mb-4">Question Distribution</h3>
              <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                <div class="bg-gray-800/50 p-4">
                  <Label for="easy-questions" class="text-green-400 font-medium block mb-2">Easy Questions</Label>
                  <Input
                    id="easy-questions"
                    name="easy-questions"
                    type="number"
                    v-model.number="manualQuestionCounts.easy"
                    min="6"
                    autocomplete="off"
                    placeholder="6"
                    class="text-base"
                  />
                  <div class="mt-1 text-xs text-gray-400">Minimum: 6 questions</div>
                </div>

                <div class="bg-gray-800/50 p-4">
                  <Label for="intermediate-questions" class="text-yellow-400 font-medium block mb-2">Intermediate Questions</Label>
                  <Input
                    id="intermediate-questions"
                    name="intermediate-questions"
                    type="number"
                    v-model.number="manualQuestionCounts.intermediate"
                    min="6"
                    autocomplete="off"
                    placeholder="6"
                    class="text-base"
                  />
                  <div class="mt-1 text-xs text-gray-400">Minimum: 6 questions</div>
                </div>

                <div class="bg-gray-800/50 p-4">
                  <Label for="advanced-questions" class="text-red-400 font-medium block mb-2">Advanced Questions</Label>
                  <Input
                    id="advanced-questions"
                    name="advanced-questions"
                    type="number"
                    v-model.number="manualQuestionCounts.advanced"
                    min="8"
                    autocomplete="off"
                    placeholder="8"
                    class="text-base"
                  />
                  <div class="mt-1 text-xs text-gray-400">Minimum: 8 questions</div>
                </div>
              </div>
              <div class="mt-3 text-sm text-gray-400">
                Specify the number of questions for each difficulty level. You can add more than the minimum requirements.
              </div>
            </div>

            <!-- Selected Questions Summary -->
            <div class="mb-6">
              <div class="flex justify-between items-center mb-3">
                <h3 class="text-lg font-medium text-gray-300">Selected Questions</h3>
                <span class="text-indigo-300 bg-indigo-900/30 px-3 py-1 rounded-full text-sm">
                  {{ getSelectedQuestionCount() }} selected
                </span>
              </div>

              <div class="bg-gray-800/50 p-4 min-h-[80px]">
                <div v-if="getSelectedQuestionCount() === 0" class="text-gray-500 text-sm italic flex items-center justify-center h-16">
                  No questions selected. Select questions from the list below.
                </div>
                <div v-else class="flex flex-wrap gap-2">
                  <div
                    v-for="id in getSelectedQuestionIds()"
                    :key="id"
                    class="flex items-center bg-indigo-900/40 text-indigo-200 text-sm px-3 py-1.5 rounded"
                  >
                    <span>ID: {{ id }}</span>
                    <Button
                      @click="removeQuestionFromSelection(id)"
                      variant="ghost"
                      size="xs"
                      class="ml-2 text-indigo-300 hover:text-white hover:bg-indigo-900/30 rounded px-1"
                    >
                      &times;
                    </Button>
                  </div>
                </div>
              </div>

              <!-- Selected Questions Summary for Fixed Mode -->
              <div class="mt-2">
                <div class="text-sm text-gray-400">
                  <span v-if="getSelectedQuestionCount() > 0">
                    {{ getSelectedQuestionCount() }} questions selected
                    <span v-if="getTotalManualQuestions() > 0"
                          :class="{
                            'text-green-400': getSelectedQuestionCount() === getTotalManualQuestions(),
                            'text-yellow-400': getSelectedQuestionCount() < getTotalManualQuestions(),
                            'text-red-400': getSelectedQuestionCount() > getTotalManualQuestions()
                          }">
                      ({{ getTotalManualQuestions() }} required)
                    </span>
                  </span>
                  <span v-else class="text-yellow-400">
                    Please select questions before creating the assessment
                  </span>
                </div>
              </div>
            </div>

            <!-- No Questions Available Message -->
            <div v-if="selectedSkillIds.length > 0 && availableQuestions.length === 0 && !isLoadingQuestions" class="mb-6 bg-yellow-900/20 p-4">
              <p class="text-yellow-300 flex items-start">
                <SvgIcon name="warning" class="mr-2 flex-shrink-0 mt-0.5 text-yellow-300" />
                <span>
                  No questions available for the selected skills. Please select different skills or add questions to these skills first.
                </span>
              </p>
            </div>

            <!-- Loading Questions -->
            <div v-if="isLoadingQuestions" class="mb-6 flex justify-center items-center py-6 bg-gray-800/30">
              <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-indigo-500"></div>
              <span class="ml-4 text-gray-300 text-base">Loading questions...</span>
            </div>

            <!-- Available Questions -->
            <div v-if="availableQuestions.length > 0" class="mb-6">
              <div class="flex flex-col md:flex-row md:justify-between md:items-center mb-4 space-y-3 md:space-y-0">
                <h3 class="text-lg font-medium text-gray-300">Available Questions</h3>

                <!-- Random Selection Button -->
                <Button
                  @click.prevent="selectRandomQuestions"
                  variant="generalAction"
                  size="skillButton"
                  :disabled="availableQuestions.length === 0"
                  title="Randomly select questions based on assessment requirements"
                  class="md:ml-auto"
                >
                  <span class="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    Random Select
                  </span>
                </Button>
              </div>

              <!-- Search and Filter -->
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
                <div class="md:col-span-3">
                  <Input
                    id="search-questions"
                    name="search-questions"
                    v-model="searchQuery"
                    placeholder="Search questions..."
                    class="w-full"
                  />
                </div>
                <div>
                  <select
                    id="difficulty-filter"
                    name="difficulty-filter"
                    v-model="difficultyFilter"
                    class="w-full px-3 py-2 bg-gray-800 border border-gray-700 rounded-lg text-white focus:outline-none focus:ring-2 focus:ring-indigo-500 [&>option]:bg-gray-800 [&>option]:text-white"
                  >
                    <option value="all">All Difficulties</option>
                    <option value="easy">Easy</option>
                    <option value="intermediate">Intermediate</option>
                    <option value="advanced">Advanced</option>
                  </select>
                </div>
              </div>

              <div class="bg-gray-800/30 p-4 max-h-[500px] overflow-y-auto">
                <div v-if="filteredQuestions.length === 0" class="text-center py-8 text-gray-400">
                  No questions match your filters.
                </div>
                <div v-else class="space-y-4">
                  <div v-for="question in filteredQuestions" :key="question.que_id"
                       class="p-4 bg-gray-800/50 hover:bg-gray-800/70 transition-all"
                       :class="{ 'bg-indigo-900/20 border-l-4 border-l-indigo-500': isQuestionSelected(question.que_id) }">
                    <div class="flex flex-col md:flex-row md:justify-between md:items-start gap-3">
                      <div class="flex-1">
                        <div class="flex flex-wrap items-center gap-2 mb-2">
                          <span class="px-2 py-1 text-xs rounded-full"
                                :class="{
                                  'bg-green-900/50 text-green-400': question.level === 'easy',
                                  'bg-yellow-900/50 text-yellow-400': question.level === 'intermediate',
                                  'bg-red-900/50 text-red-400': question.level === 'advanced'
                                }">
                            {{ question.level.charAt(0).toUpperCase() + question.level.slice(1) }}
                          </span>
                          <span class="text-gray-400 text-sm">ID: {{ question.que_id }}</span>
                          <span class="text-gray-400 text-sm">{{ question.skill_name }}</span>
                        </div>
                        <div class="text-white">{{ question.question }}</div>
                      </div>
                      <div class="md:ml-4 flex-shrink-0">
                        <Button
                          @click="addQuestionToSelection(question.que_id)"
                          variant="generalAction"
                          size="skillButton"
                          :class="isQuestionSelected(question.que_id)
                            ? 'bg-indigo-700/70 text-indigo-200 hover:bg-indigo-600/70'
                            : 'bg-indigo-900/50 text-indigo-400 hover:bg-indigo-800/50'"
                        >
                          {{ isQuestionSelected(question.que_id) ? 'Selected' : 'Select' }}
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </section>

          <!-- Submit button -->
          <section class="w-full mt-8 mb-4">
            <div class="flex justify-end">
              <button
                type="submit"
                :disabled="isLoading || isLoadingQuestions || selectedSkillIds.length === 0"
                class="btn-phantom px-6 py-3"
                :class="{'opacity-50 cursor-not-allowed': selectedSkillIds.length === 0}"
              >
                <span class="flex items-center">
                  <SvgIcon v-if="isLoading" name="spinner" class="-ml-1 mr-2 text-white animate-spin" />
                  <SvgIcon v-else name="plus" class="mr-2" />
                  {{ isLoading ? 'Creating...' : 'Create Assessment' }}
                </span>
              </button>
            </div>
          </section>
        </form>
      </div>
    </PhantomLayout>
  </template>

  <script setup>
  import { ref, onMounted, onUnmounted, computed, watch } from 'vue';
  import { useRouter } from 'vue-router';
  import { api } from '@/services/api';
  import { getErrorMessage, logError } from '@/utils/errorHandling';
  import { useMessageHandler } from '@/utils/messageHandler';
  import { extractResponseData, extractErrorInfo } from '@/utils/apiResponseHandler';
  import { safeAddEventListener } from '@/utils/domHelpers';
  import { decodeSkillId } from '@/utils/hashIds';
  import PhantomLayout from '@/components/layout/Layout.vue';
  import { Alert, AlertDescription } from '@/components/ui/alert';
  import { Label } from '@/components/ui/label';
  import { Input } from '@/components/ui/input';
  import { Button } from '@/components/ui/button';
  import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
  } from '@/components/ui/select';

  const router = useRouter();
  const navigateTo = (path) => {
    router.push(path);
  };

  // Message handling
  const { message, isSuccess, setErrorMessage, setSuccessMessage, clearMessage } = useMessageHandler();

  // Form data
  const assessmentName = ref('');
  const assessmentDescription = ref('');
  const selectedSkillIds = ref([]); // Changed to array for multiple selection
  const questionSelectionMode = ref('dynamic'); // Default to dynamic mode
  const assessmentDuration = ref(30); // Default duration in minutes
  const hasInteractedWithSkills = ref(false); // Track if user has interacted with skills
  const skills = ref([]);
  const isLoading = ref(false);
  const createdAssessmentDetails = ref(null);

  // Fixed questions data
  const availableQuestions = ref([]);
  const questionIds = ref('');
  const isLoadingQuestions = ref(false);
  const manualQuestionCounts = ref({
    easy: 6,
    intermediate: 6,
    advanced: 8
  });
  const searchQuery = ref('');
  const difficultyFilter = ref('all');

  // Computed property for valid selected skill IDs
  const validSelectedSkillIds = computed(() => {
    return selectedSkillIds.value.filter(id =>
      id !== null && id !== undefined && id !== ''
    );
  });

  // Helper functions for skill management
  const getSkillName = (skillId) => {
    // Defensive check to ensure skills.value is an array
    if (!Array.isArray(skills.value)) {
      console.warn('Skills is not an array:', skills.value);
      return `Skill ${skillId}`;
    }

    // Handle null or undefined skillId
    if (skillId === null || skillId === undefined) {
      console.warn('Null or undefined skillId passed to getSkillName');
      return 'Unknown Skill';
    }

    // First, try to find by hash ID (frontend display ID)
    let skill = skills.value.find(s => s.id === skillId);

    // If not found by hash ID, try to find by numeric ID
    if (!skill) {
      // If skillId is numeric, try to find by numericId field
      if (typeof skillId === 'number' || /^\d+$/.test(skillId)) {
        const numericId = typeof skillId === 'number' ? skillId : parseInt(skillId, 10);
        skill = skills.value.find(s => s.numericId === numericId);
      }
    }

    // Return the skill name if found
    if (skill && skill.name) {
      return skill.name;
    } else {
      // If no skill found, return a formatted skill ID
      console.warn(`No skill found with ID: ${skillId} (checked both hash and numeric)`);
      return `Skill ${skillId}`;
    }
  };

  const removeSkill = (skillId) => {
    selectedSkillIds.value = selectedSkillIds.value.filter(id => id !== skillId);
    // Clean up any remaining null/undefined values
    cleanupSelectedSkillIds();
    // Fetch questions again if in fixed mode
    if (questionSelectionMode.value === 'fixed' && selectedSkillIds.value.length > 0) {
      fetchQuestionsForSkills();
    } else if (selectedSkillIds.value.length === 0) {
      availableQuestions.value = [];
    }
  };

  // Clean up the selectedSkillIds array to remove null/undefined values
  const cleanupSelectedSkillIds = () => {
    const originalLength = selectedSkillIds.value.length;
    selectedSkillIds.value = selectedSkillIds.value.filter(id =>
      id !== null && id !== undefined && id !== ''
    );

    if (selectedSkillIds.value.length !== originalLength) {
      console.log('Cleaned up selectedSkillIds, removed null/undefined values');
    }
  };

  // This function is called when the skill selection changes
  const onSkillSelectionChange = () => {
    // First, clean up any null/undefined values
    cleanupSelectedSkillIds();

    // Validate that all selected skill IDs exist in the skills array
    if (selectedSkillIds.value.length > 0) {
      const validIds = selectedSkillIds.value.filter(id => {
        // Check if this ID exists in the skills array
        const exists = skills.value.some(skill => skill.id === id);
        if (!exists) {
          console.warn(`Removing invalid skill ID: ${id} - skill not found in loaded skills`);
        }
        return exists;
      });

      // Update the selectedSkillIds with validated IDs only if there were invalid ones
      if (validIds.length !== selectedSkillIds.value.length) {
        console.log(`Cleaned up skill selection: ${selectedSkillIds.value.length} -> ${validIds.length} valid skills`);
        selectedSkillIds.value = validIds;
      }
    }

    // The watcher will automatically handle fetching questions
    hasInteractedWithSkills.value = true;
  };

  // Fetch skills from API
  const fetchSkills = async () => {
    try {
      isLoading.value = true;
      const response = await api.admin.getSkills({ limit: 100, offset: 0 });

      // Use standardized response data extraction
      const skillsData = extractResponseData(response);

      if (Array.isArray(skillsData)) {
        console.log('Fetched skills from API:', skillsData.length, 'skills found');
        console.log('Raw skills data sample:', skillsData.slice(0, 2)); // Show first 2 skills for debugging

        // Basic filtering - only filter out null/undefined skills
        const filteredSkills = skillsData.filter(skill => {
          // Basic validation - must have an object
          if (!skill) {
            console.warn('Filtering out null/undefined skill');
            return false;
          }

          return true;
        });

        // Normalize skills - keep both hash ID for frontend and numeric ID for API
        const normalizedSkills = filteredSkills.map(skill => {
          console.log('Processing skill for normalization:', skill);

          // For display purposes, use id_hash (for security/obfuscation) or fallback to id
          const displayId = skill.id_hash || skill.id;

          // For API calls, we need the numeric ID
          // If skill.id is numeric, use it; if it's a hash, we'll decode it later
          let numericId = null;

          // Check if skill.id is already a numeric ID
          if (typeof skill.id === 'number' && skill.id > 0) {
            numericId = skill.id;
          } else if (typeof skill.id === 'string' && /^\d+$/.test(skill.id)) {
            numericId = parseInt(skill.id, 10);
          }
          // If we have a numeric_id field, use that
          else if (skill.numeric_id && (typeof skill.numeric_id === 'number' || /^\d+$/.test(skill.numeric_id))) {
            numericId = typeof skill.numeric_id === 'number' ? skill.numeric_id : parseInt(skill.numeric_id, 10);
          }
          // If we don't have a numeric ID but have a hash, we'll need to decode it
          else if (skill.id_hash || (typeof skill.id === 'string' && !/^\d+$/.test(skill.id))) {
            // Mark this skill as needing hash decoding
            numericId = 'NEEDS_DECODING';
          }

          if (!displayId) {
            console.warn('Skill without display ID:', skill);
            return null;
          }

          try {
            return {
              ...skill,
              id: displayId, // Use hash ID for frontend display and selection
              numericId: numericId, // Store numeric ID for API calls (or 'NEEDS_DECODING')
              name: skill.name || 'Unnamed Skill'
            };
          } catch (e) {
            console.warn('Error normalizing skill:', e);
            return null;
          }
        }).filter(skill => skill !== null);

        skills.value = normalizedSkills;
        console.log('Skills loaded successfully:', skills.value.length, 'skills');

        // Clean up any invalid selected skill IDs after skills are loaded
        cleanupSelectedSkillIds();
      } else {
        // Try to handle non-array responses more gracefully
        console.warn('Response is not an array:', skillsData);

        // If it's an object with items or data property, try to use that
        if (skillsData && typeof skillsData === 'object') {
          const possibleArrays = ['items', 'data', 'skills', 'results'];
          for (const prop of possibleArrays) {
            if (Array.isArray(skillsData[prop])) {
              console.log(`Found skills array in response.${prop}`);
              skills.value = skillsData[prop];
              return;
            }
          }
        }

        // If we get here, we couldn't find a valid skills array
        console.warn('Invalid skills response format:', response);
        skills.value = [];
        setErrorMessage('No skills found or invalid response format');
      }
    } catch (error) {
      const errorInfo = extractErrorInfo(error);
      console.error('Error in fetchSkills:', errorInfo);
      logError(error, 'fetchSkills');
      setErrorMessage(errorInfo.message || 'Failed to fetch skills');
      skills.value = []; // Ensure skills is always an array
    } finally {
      isLoading.value = false;
    }
  };

  // Create assessment via API
  const createAssessment = async () => {
    // Set interaction flag when form is submitted
    hasInteractedWithSkills.value = true;

    // Enhanced validation
    if (!assessmentName.value || !assessmentDescription.value || selectedSkillIds.value.length === 0) {
      setErrorMessage('Please fill in all required fields and select at least one skill');
      return;
    }

    // Additional validation
    if (assessmentName.value.trim().length < 3) {
      setErrorMessage('Assessment name must be at least 3 characters long');
      return;
    }

    if (assessmentName.value.trim().length > 255) {
      setErrorMessage('Assessment name must be less than 255 characters');
      return;
    }

    if (assessmentDescription.value.trim().length < 20) {
      setErrorMessage('Assessment description must be at least 20 characters long');
      return;
    }

    if (assessmentDescription.value.trim().length > 2000) {
      setErrorMessage('Assessment description must be less than 2000 characters');
      return;
    }

    if (selectedSkillIds.value.length > 10) {
      setErrorMessage('Please select no more than 10 skills for an assessment');
      return;
    }

    // Validate valid selected skill IDs
    const validSkillCount = validSelectedSkillIds.value.length;
    if (validSkillCount === 0) {
      setErrorMessage('Please select at least one valid skill');
      return;
    }

    // Additional validation for fixed mode
    if (questionSelectionMode.value === 'fixed') {
      const selectedQuestionCount = getSelectedQuestionCount();
      const requiredQuestionCount = getTotalManualQuestions();

      if (selectedQuestionCount === 0) {
        setErrorMessage('Please select questions for your fixed assessment');
        return;
      }

      if (selectedQuestionCount !== requiredQuestionCount) {
        setErrorMessage(`You have selected ${selectedQuestionCount} questions but specified ${requiredQuestionCount} questions in the distribution. Please adjust either your selection or the distribution to match.`);
        return;
      }

      // Validate minimum requirements
      if (manualQuestionCounts.value.easy < 6) {
        setErrorMessage('Easy questions must be at least 6');
        return;
      }
      if (manualQuestionCounts.value.intermediate < 6) {
        setErrorMessage('Intermediate questions must be at least 6');
        return;
      }
      if (manualQuestionCounts.value.advanced < 8) {
        setErrorMessage('Advanced questions must be at least 8');
        return;
      }
    }

    isLoading.value = true;
    clearMessage();
    createdAssessmentDetails.value = null;

    try {
      // Get the primary skill for the topic (using the first selected skill)
      const primarySkillId = selectedSkillIds.value[0];
      const primarySkill = skills.value.find(skill => skill.id === primarySkillId);

      if (!primarySkill) {
        throw new Error('Selected skill not found');
      }

      // Get current username (in a real app, this would come from auth)
      const username = localStorage.getItem('username') || 'admin_user';

      // Convert hash IDs to numeric IDs for API (preserving the hash system)
      const skillsToProcess = validSelectedSkillIds.value
        .map(hashId => {
          if (hashId === null || hashId === undefined || hashId === '') {
            return null;
          }

          // Find the skill by hash ID
          const skill = skills.value.find(skill => skill.id === hashId);
          if (!skill) {
            console.warn(`Skill with hash ID ${hashId} not found in loaded skills`);
            return null;
          }

          return { hashId, skill };
        })
        .filter(item => item !== null);

      if (skillsToProcess.length === 0) {
        throw new Error('No valid skills selected.');
      }

      // Process each skill to get numeric ID (decode if necessary)
      const validSkillIds = [];

      for (const { hashId, skill } of skillsToProcess) {
        let numericId = null;

        // If we already have a numeric ID, use it
        if (skill.numericId && skill.numericId !== 'NEEDS_DECODING' && !isNaN(skill.numericId)) {
          numericId = skill.numericId;
        }
        // If we need to decode, decode the hash ID
        else if (skill.numericId === 'NEEDS_DECODING' || !skill.numericId) {
          try {
            numericId = await decodeSkillId(hashId);
            if (!numericId || isNaN(numericId)) {
              console.warn(`Failed to decode skill hash ${hashId} or got invalid numeric ID:`, numericId);
              continue;
            }
          } catch (error) {
            console.warn(`Error decoding skill hash ${hashId}:`, error);
            continue;
          }
        }

        if (numericId && !isNaN(numericId) && numericId > 0) {
          validSkillIds.push(numericId);

          // Store the decoded numeric ID back to the skill object for future lookups
          if (skill.numericId === 'NEEDS_DECODING' || !skill.numericId) {
            skill.numericId = numericId;
          }
        } else {
          console.warn(`Skill ${hashId} doesn't have a valid numeric ID:`, numericId);
        }
      }

      if (validSkillIds.length === 0) {
        throw new Error('No valid numeric skill IDs found. Please check that skills have proper numeric IDs or hash decoding is working.');
      }

      // Validate that all IDs are integers (API requirement)
      const nonIntegerIds = validSkillIds.filter(id => !Number.isInteger(id));
      if (nonIntegerIds.length > 0) {
        throw new Error(`Some skill IDs are not integers: ${nonIntegerIds.join(', ')}`);
      }

      // Validate duration
      const durationValue = parseInt(assessmentDuration.value);
      if (isNaN(durationValue) || durationValue < 5 || durationValue > 180) {
        throw new Error('Duration must be a valid number between 5 and 180 minutes');
      }

      console.log('Available skills in frontend:', skills.value.map(s => ({
        hashId: s.id,
        numericId: s.numericId,
        name: s.name
      })));
      console.log('Selected skill hash IDs:', selectedSkillIds.value);
      console.log('Converted numeric IDs to send to API:', validSkillIds);
      console.log('ID conversion mapping:', validSelectedSkillIds.value.map(hashId => {
        const skill = skills.value.find(s => s.id === hashId);
        return { hashId, numericId: skill?.numericId, name: skill?.name };
      }));
      console.log('Assessment data to send:', {
        quiz_name: assessmentName.value,
        topic: assessmentDescription.value,
        user_id: username,
        skill_ids: validSkillIds,
        question_selection_mode: questionSelectionMode.value,
        duration: durationValue,
        create_single_assessment: true
      });

      // Call the API to create the quiz/assessment
      const response = await api.admin.createAssessment({
        quiz_name: assessmentName.value.trim(),
        topic: assessmentDescription.value.trim(), // Use the new description field instead of skill description
        user_id: username.trim(),
        skill_ids: validSkillIds,
        question_selection_mode: questionSelectionMode.value,
        duration: durationValue, // Add duration in minutes
        create_single_assessment: true // Create only one assessment instead of mock and final
      });

      // Use standardized response data extraction
      const responseData = extractResponseData(response);

      // Store the response details for display
      createdAssessmentDetails.value = responseData;

      // If fixed mode and questions are selected, automatically add them
      if (questionSelectionMode.value === 'fixed') {
        await fetchAssessmentQuestions();

        // If questions were pre-selected, add them automatically
        if (getSelectedQuestionCount() > 0) {
          try {
            await addFixedQuestions();
            setSuccessMessage(`Successfully created "${assessmentName.value}" assessment with ${getSelectedQuestionCount()} fixed questions!`);
          } catch (error) {
            // Error is already handled in addFixedQuestions function
            console.warn('Failed to auto-add selected questions:', error);
            setSuccessMessage(`Assessment "${assessmentName.value}" created, but failed to add questions. Please add them manually.`);
          }
        } else {
          setSuccessMessage(`Successfully created "${assessmentName.value}" assessment!`);
        }
      } else {
        setSuccessMessage(`Successfully created "${assessmentName.value}" assessment!`);
      }

      // Reset form after success (but keep selected questions for fixed mode)
      assessmentName.value = '';
      selectedSkillIds.value = [];
      assessmentDuration.value = 30; // Reset to default duration

    } catch (error) {
      const errorInfo = extractErrorInfo(error);
      logError(error, 'createAssessment');

      // Check if the error is related to skills not being found
      if (errorInfo.message && errorInfo.message.includes('not found')) {
        setErrorMessage('Some selected skills no longer exist in the database. Please refresh the page and reselect your skills.');
        // Automatically refresh skills to get the latest data
        try {
          await fetchSkills();
          console.log('Refreshed skills after error');
        } catch (refreshError) {
          console.warn('Failed to refresh skills:', refreshError);
        }
      } else {
        setErrorMessage(errorInfo.message || 'An unexpected error occurred while creating the assessment');
      }

      createdAssessmentDetails.value = null;
    } finally {
      isLoading.value = false;
    }
  };

  // Fixed Questions Functions
  const fetchAssessmentQuestions = async () => {
    if (!createdAssessmentDetails.value?.assessment_id) {
      return;
    }

    isLoadingQuestions.value = true;
    try {
      const questionsResponse = await api.admin.getAssessmentQuestions(createdAssessmentDetails.value.assessment_id);
      const questionData = extractResponseData(questionsResponse);
      availableQuestions.value = questionData?.questions || [];
    } catch (error) {
      logError(error, 'fetchAssessmentQuestions');
      setErrorMessage(getErrorMessage(error, 'Failed to fetch questions for this assessment'));
    } finally {
      isLoadingQuestions.value = false;
    }
  };

  // Fetch questions for selected skills (used when in fixed mode)
  const fetchQuestionsForSkills = async () => {
    if (selectedSkillIds.value.length === 0) {
      availableQuestions.value = [];
      return;
    }

    isLoadingQuestions.value = true;
    try {
      // Fetch questions for all selected skills
      const allQuestions = [];
      for (const skillId of selectedSkillIds.value) {
        try {
          const response = await api.admin.getSkillQuestions(skillId);
          const responseData = extractResponseData(response);

          if (responseData && responseData.questions) {
            // Add skill_name to each question for display purposes
            const questionsWithSkillName = responseData.questions.map(question => ({
              ...question,
              skill_name: responseData.skill_name
            }));
            allQuestions.push(...questionsWithSkillName);
          }
        } catch (error) {
          console.warn(`Failed to fetch questions for skill ${skillId}:`, error);
        }
      }

      // Remove duplicates based on question ID
      const uniqueQuestions = allQuestions.filter((question, index, self) =>
        index === self.findIndex(q => q.que_id === question.que_id)
      );

      availableQuestions.value = uniqueQuestions;
    } catch (error) {
      logError(error, 'fetchQuestionsForSkills');
      setErrorMessage(getErrorMessage(error, 'Failed to fetch questions for selected skills'));
    } finally {
      isLoadingQuestions.value = false;
    }
  };

  // Computed property for filtered questions
  const filteredQuestions = computed(() => {
    let filtered = availableQuestions.value;

    // Filter by search query
    if (searchQuery.value) {
      const query = searchQuery.value.toLowerCase();
      filtered = filtered.filter(q =>
        q.question.toLowerCase().includes(query) ||
        q.skill_name.toLowerCase().includes(query)
      );
    }

    // Filter by difficulty
    if (difficultyFilter.value !== 'all') {
      filtered = filtered.filter(q => q.level === difficultyFilter.value);
    }

    return filtered;
  });

  // Helper methods for question selection
  const getSelectedQuestionIds = () => {
    return questionIds.value.split(',')
      .map(id => id.trim())
      .filter(id => id)
      .map(id => parseInt(id));
  };

  const getSelectedQuestionCount = () => {
    return getSelectedQuestionIds().length;
  };

  const getTotalManualQuestions = () => {
    return manualQuestionCounts.value.easy + manualQuestionCounts.value.intermediate + manualQuestionCounts.value.advanced;
  };

  const isQuestionSelected = (questionId) => {
    return getSelectedQuestionIds().includes(questionId);
  };

  const addQuestionToSelection = (questionId) => {
    const selectedIds = getSelectedQuestionIds();

    if (selectedIds.includes(questionId)) {
      // Remove the question if already selected
      removeQuestionFromSelection(questionId);
    } else {
      // Add the question if not already selected
      selectedIds.push(questionId);
      questionIds.value = selectedIds.join(', ');
    }
  };

  const removeQuestionFromSelection = (questionId) => {
    const selectedIds = getSelectedQuestionIds();
    const updatedIds = selectedIds.filter(id => id !== questionId);
    questionIds.value = updatedIds.join(', ');
  };

  // Randomly select questions based on assessment requirements
  const selectRandomQuestions = () => {
    // Validate manual question counts meet minimum requirements
    if (manualQuestionCounts.value.easy < 6) {
      setErrorMessage('Easy questions must be at least 6. You can specify more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.intermediate < 6) {
      setErrorMessage('Intermediate questions must be at least 6. You can specify more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.advanced < 8) {
      setErrorMessage('Advanced questions must be at least 8. You can specify more than 8 if needed.');
      return;
    }

    // Group questions by difficulty
    const easyQuestions = availableQuestions.value.filter(q => q.level === 'easy');
    const intermediateQuestions = availableQuestions.value.filter(q => q.level === 'intermediate');
    const advancedQuestions = availableQuestions.value.filter(q => q.level === 'advanced');

    // Check if we have enough questions for each difficulty
    const easyCount = manualQuestionCounts.value.easy;
    const intermediateCount = manualQuestionCounts.value.intermediate;
    const advancedCount = manualQuestionCounts.value.advanced;

    if (easyQuestions.length < easyCount) {
      setErrorMessage(`Not enough easy questions available. Required: ${easyCount}, Available: ${easyQuestions.length}`);
      return;
    }
    if (intermediateQuestions.length < intermediateCount) {
      setErrorMessage(`Not enough intermediate questions available. Required: ${intermediateCount}, Available: ${intermediateQuestions.length}`);
      return;
    }
    if (advancedQuestions.length < advancedCount) {
      setErrorMessage(`Not enough advanced questions available. Required: ${advancedCount}, Available: ${advancedQuestions.length}`);
      return;
    }

    // Shuffle array function
    const shuffleArray = (array) => {
      const shuffled = [...array];
      for (let i = shuffled.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
      }
      return shuffled;
    };

    // Shuffle and select questions by difficulty
    const selectedEasy = shuffleArray([...easyQuestions]).slice(0, easyCount);
    const selectedIntermediate = shuffleArray([...intermediateQuestions]).slice(0, intermediateCount);
    const selectedAdvanced = shuffleArray([...advancedQuestions]).slice(0, advancedCount);

    // Combine all selected questions
    const selectedQuestions = [...selectedEasy, ...selectedIntermediate, ...selectedAdvanced];

    // Update the selection
    questionIds.value = selectedQuestions.map(q => q.que_id).join(', ');

    // Show success message
    setSuccessMessage(`Randomly selected ${selectedQuestions.length} questions based on assessment requirements.`);
  };

  // Add fixed questions via API
  const addFixedQuestions = async () => {
    if (!createdAssessmentDetails.value?.assessment_id) {
      setErrorMessage('Please create the assessment first before adding fixed questions');
      return;
    }

    const selectedIds = getSelectedQuestionIds();
    if (selectedIds.length === 0) {
      setErrorMessage('Please select at least one question');
      return;
    }

    // Validate minimum requirements
    if (manualQuestionCounts.value.easy < 6) {
      setErrorMessage('Easy questions must be at least 6. You can add more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.intermediate < 6) {
      setErrorMessage('Intermediate questions must be at least 6. You can add more than 6 if needed.');
      return;
    }
    if (manualQuestionCounts.value.advanced < 8) {
      setErrorMessage('Advanced questions must be at least 8. You can add more than 8 if needed.');
      return;
    }

    // Check if selected questions match the manual counts
    const totalSelected = selectedIds.length;
    const totalRequired = getTotalManualQuestions();

    if (totalSelected !== totalRequired) {
      setErrorMessage(`You have selected ${totalSelected} questions but specified ${totalRequired} questions in the manual entry. Please adjust either your selection or the manual counts to match.`);
      return;
    }

    isLoadingQuestions.value = true;
    clearMessage();

    try {
      // Call the API to add fixed questions
      await api.admin.addFinalQuestions({
        assessment_id: parseInt(createdAssessmentDetails.value.assessment_id),
        question_ids: selectedIds,
        quiz_name: createdAssessmentDetails.value.assessment_base_name || 'Assessment',
        question_distribution: {
          easy: manualQuestionCounts.value.easy,
          intermediate: manualQuestionCounts.value.intermediate,
          advanced: manualQuestionCounts.value.advanced,
             total: getTotalManualQuestions()
        }
      });

      // Don't show success message here - let the calling function handle it

      // Clear the selection after success
      questionIds.value = '';

    } catch (error) {
      logError(error, 'addFixedQuestions');
      setErrorMessage(getErrorMessage(error, 'An unexpected error occurred while assigning questions'));
    } finally {
      isLoadingQuestions.value = false;
    }
  };

  // Watchers to automatically fetch questions when skills or mode changes
  watch([selectedSkillIds, questionSelectionMode], () => {
    if (questionSelectionMode.value === 'fixed' && selectedSkillIds.value.length > 0) {
      fetchQuestionsForSkills();
    } else {
      availableQuestions.value = [];
      questionIds.value = '';
    }
  }, { deep: true });

  // Clear all selected skills
  const clearSkillSelection = () => {
    selectedSkillIds.value = [];
    cleanupSelectedSkillIds(); // Clean up any remaining values
    hasInteractedWithSkills.value = true;
    onSkillSelectionChange();
  };

  onMounted(() => {
    fetchSkills();
  });
  </script>
