version: '3.8'

services:
  db:
    image: postgres:13
    container_name: herbit-db
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: ${PG_DATABASE}
      POSTGRES_USER: ${PG_USER}
      POSTGRES_PASSWORD: ${PG_PASSWORD}
    volumes:
      - db-data:/var/lib/postgresql/data
    networks:
      - herbit-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${PG_USER} -d ${PG_DATABASE}"]
      interval: 10s
      timeout: 5s
      retries: 5
      start_period: 10s

  redis:
    image: redis:7-alpine
    container_name: herbit-redis
    restart: unless-stopped
    ports:
      - "6380:6379"
    networks:
      - herbit-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  backend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: herbit-backend
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      dex:
        condition: service_started
    env_file:
      - .env
    environment:
      PG_HOST: db
      DAPR_HTTP_PORT: 3500
      DOCKER_ENV: "true"
      # Override Auth URLs for Docker environment
      AUTH_ISSUER: http://dex:5556
      AUTH_TOKEN_URL: http://dex:5556/token
      AUTH_AUTHORIZE_URL: http://dex:5556/auth
      AUTH_LOGOUT_URL: http://dex:5556/logout
      AUTH_JWKS_URI: http://dex:5556/keys
    ports:
      - "${SERVER_PORT}:${SERVER_PORT}"
      - "3500:3500"
    networks:
      - herbit-network
    volumes:
      - ./dapr/components:/app/dapr/components

  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
    container_name: herbit-worker
    restart: unless-stopped
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
    env_file:
      - .env
    environment:
      PG_HOST: db
      WORKER_PORT: 8001
      DAPR_HTTP_PORT: 3501
    ports:
      - "8001:8001"
      - "3501:3501"
    networks:
      - herbit-network
    volumes:
      - ./dapr/components:/app/dapr/components
      - ./dapr/subscription.yaml:/app/dapr/subscription.yaml

  frontend:
    build:
      context: ./quiz-management-ui
      dockerfile: Dockerfile
    container_name: herbit-frontend
    restart: unless-stopped
    depends_on:
      - backend
    ports:
      - "5173:5173"
    environment:
      - VITE_API_BASE_URL=http://localhost:8000
    volumes:
      - ./quiz-management-ui:/app
      - /app/node_modules
    networks:
      - herbit-network

  adminer:
    image: adminer:latest
    container_name: herbit-adminer
    restart: unless-stopped
    depends_on:
      - db
    ports:
      - "8080:8080"
    environment:
      ADMINER_DEFAULT_SERVER: db
      ADMINER_DESIGN: pepa-linha
    networks:
      - herbit-network

  ldap:
    image: osixia/openldap:latest
    container_name: herbit-ldap
    command: ["--copy-service"]
    environment:
      LDAP_ORGANISATION: "Example Inc."
      LDAP_DOMAIN: "example.org"
      LDAP_ADMIN_PASSWORD: admin
      LDAP_TLS_VERIFY_CLIENT: try
    volumes:
      - ./dex/config-ldap.ldif:/container/service/slapd/assets/config/bootstrap/ldif/custom/config-ldap.ldif
    ports:
      - "389:389"
      - "636:636"
    networks:
      - herbit-network

  dex:
    image: ghcr.io/dexidp/dex:v2.37.0
    container_name: herbit-dex
    command: ["dex", "serve", "/etc/dex/config.yaml"]
    depends_on:
      - ldap
    ports:
      - "5556:5556"
    volumes:
      - ./dex/config-ldap.yaml:/etc/dex/config.yaml
    networks:
      - herbit-network



networks:
  herbit-network:
    driver: bridge

volumes:
  db-data:
    name: herbit-db-data
  dex-data:
    name: herbit-dex-data
