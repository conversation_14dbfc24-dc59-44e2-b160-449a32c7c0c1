# Already included in default config of Docker image osixia/openldap:1.4.0.
#
# dn: dc=example,dc=org
# objectClass: dcObject
# objectClass: organization
# o: Example Company
# dc: example

dn: ou=People,dc=example,dc=org
objectClass: organizationalUnit
ou: People

dn: cn=jane,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: doe
cn: jane
mail: <EMAIL>
userpassword: foo

dn: cn=john,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: doe
cn: john
mail: <EMAIL>
userpassword: bar

# New users

dn: cn=rakshit,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: padariya
cn: rakshit
mail: <EMAIL>
userpassword: p1234

dn: cn=priyanshee,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: padariya
cn: priyanshee
mail: <EMAIL>
userpassword: p1234

dn: cn=pratiksha,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: pratiksha
cn: pratiksha
mail: <EMAIL>
userpassword: p1234

dn: cn=rahul,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: rahul
cn: rahul
mail: <EMAIL>
userpassword: p1234

dn: cn=kunjan,ou=People,dc=example,dc=org
objectClass: person
objectClass: inetOrgPerson
sn: kunjan
cn: kunjan
mail: <EMAIL>
userpassword: p1234

# Group definitions.

dn: ou=Groups,dc=example,dc=org
objectClass: organizationalUnit
ou: Groups

dn: cn=admins,ou=Groups,dc=example,dc=org
objectClass: groupOfNames
cn: admins
member: cn=john,ou=People,dc=example,dc=org
member: cn=jane,ou=People,dc=example,dc=org
member: cn=rakshit,ou=People,dc=example,dc=org

dn: cn=developers,ou=Groups,dc=example,dc=org
objectClass: groupOfNames
cn: developers
member: cn=jane,ou=People,dc=example,dc=org
member: cn=priyanshee,ou=People,dc=example,dc=org

dn: cn=employees,ou=Groups,dc=example,dc=org
objectClass: groupOfNames
cn: employees
member: cn=rakshit,ou=People,dc=example,dc=org
member: cn=rahul,ou=People,dc=example,dc=org
member: cn=priyanshee,ou=People,dc=example,dc=org
member: cn=pratiksha,ou=People,dc=example,dc=org
member: cn=kunjan,ou=People,dc=example,dc=org
