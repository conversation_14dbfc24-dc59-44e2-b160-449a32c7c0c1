"""
Background Worker for Question Generation using Dapr Pub/Sub
Handles question generation tasks published by the admin API.
"""

import asyncio
import json
import logging
import os
from typing import Any, Dict

import psycopg2
import psycopg2.extras
from fastapi import FastAPI, HTTPException, Request
from pydantic import BaseModel

from config import DATABASE_CONFIG
from quiz_que_generate import ask_for_question

# Set up logging
log_level = os.getenv("LOG_LEVEL", "INFO").upper()
logging.basicConfig(
    level=getattr(logging, log_level),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[logging.StreamHandler()],
)
logger = logging.getLogger(__name__)

# Create FastAPI app for the worker
app = FastAPI(
    title="Question Generation Worker",
    description="Background worker for generating quiz questions",
    version="1.0.0",
)


class QuestionGenerationTask(BaseModel):
    """Model for question generation task data"""

    skill_id: int
    skill_name: str
    skill_description: str
    task_id: str = None  # Optional task identifier for tracking


@app.post("/generate-questions")
async def handle_question_generation(request: Request):
    """
    Dapr subscription endpoint for question generation tasks.
    This endpoint is called by Dapr when a message is published to the generate-questions topic.
    """
    try:
        # Get the message data from Dapr
        body = await request.body()
        data = json.loads(body)

        # Extract the actual task data (Dapr wraps it in 'data' field)
        task_data = data.get("data", data)
        logger.info(f"Received question generation task: {task_data}")

        # Validate the task data
        try:
            task = QuestionGenerationTask(**task_data)
        except Exception as e:
            logger.error(f"Invalid task data format: {e}")
            raise HTTPException(status_code=400, detail=f"Invalid task data: {str(e)}")

        # Start question generation in background
        asyncio.create_task(generate_questions_background(task))

        # Return success immediately (Dapr expects 200 for successful message processing)
        return {"status": "accepted", "message": "Question generation task started"}

    except json.JSONDecodeError as e:
        logger.error(f"Failed to parse request body: {e}")
        raise HTTPException(status_code=400, detail="Invalid JSON in request body")
    except Exception as e:
        logger.error(f"Error processing question generation task: {e}")
        # Return 500 to trigger Dapr retries
        raise HTTPException(status_code=500, detail=f"Task processing failed: {str(e)}")


async def generate_questions_background(task: QuestionGenerationTask):
    """
    Background task to generate questions for a skill.
    This runs asynchronously without blocking the endpoint response.
    """
    try:
        logger.info(
            f"Starting question generation for skill: {task.skill_name} (ID: {task.skill_id})"
        )

        # Update task status to "in_progress" if we had a tracking system
        if task.task_id:
            await update_task_status(task.task_id, "in_progress")

        # Use existing question count from database to determine how many questions exist
        question_count_before = await get_question_count_for_skill(task.skill_id)
        logger.info(
            f"Skill {task.skill_name} currently has {question_count_before} questions"
        )

        # Generate questions using the existing function
        quiz_name = f"skill_{task.skill_id}_{task.skill_name}"
        topics = task.skill_description

        result = await ask_for_question(quiz_name, topics, skill_id=task.skill_id)

        if result:
            # Get new question count
            question_count_after = await get_question_count_for_skill(task.skill_id)
            questions_generated = question_count_after - question_count_before

            logger.info(
                f"Successfully generated {questions_generated} questions for skill: {task.skill_name}"
            )

            # Update task status to "completed" if we had a tracking system
            if task.task_id:
                await update_task_status(
                    task.task_id,
                    "completed",
                    {
                        "questions_generated": questions_generated,
                        "total_questions": question_count_after,
                    },
                )
        else:
            logger.error(f"Failed to generate questions for skill: {task.skill_name}")

            # Update task status to "failed" if we had a tracking system
            if task.task_id:
                await update_task_status(
                    task.task_id,
                    "failed",
                    {"error": "Question generation returned None"},
                )

    except Exception as e:
        logger.error(
            f"Error in background question generation for skill {task.skill_name}: {e}",
            exc_info=True,
        )

        # Update task status to "failed" if we had a tracking system
        if task.task_id:
            await update_task_status(task.task_id, "failed", {"error": str(e)})


async def get_question_count_for_skill(skill_id: int) -> int:
    """Get the current number of questions for a skill"""
    try:
        with psycopg2.connect(**DATABASE_CONFIG) as conn:
            with conn.cursor(cursor_factory=psycopg2.extras.DictCursor) as cur:
                cur.execute(
                    "SELECT COUNT(*) as count FROM questions WHERE skill_id = %s",
                    (skill_id,),
                )
                result = cur.fetchone()
                return result["count"] if result else 0
    except Exception as e:
        logger.error(f"Error getting question count for skill {skill_id}: {e}")
        return 0


async def update_task_status(
    task_id: str, status: str, metadata: Dict[str, Any] = None
):
    """
    Update task status in database (optional - implement if you want task tracking)
    For now, this is just a placeholder for future implementation
    """
    logger.info(f"Task {task_id} status updated to: {status}")
    if metadata:
        logger.info(f"Task {task_id} metadata: {metadata}")


@app.get("/health")
async def health_check():
    """Health check endpoint"""
    return {"status": "healthy", "service": "question-generation-worker"}


@app.get("/")
async def root():
    """Root endpoint"""
    return {"message": "Question Generation Worker", "status": "running"}


if __name__ == "__main__":
    import uvicorn

    port = int(os.getenv("WORKER_PORT", "8001"))
    uvicorn.run(app, host="0.0.0.0", port=port)
